namespace DynamicFreesql.Core.Common;

/// <summary>
/// 动态查询异常基类
/// </summary>
public class DynamicQueryException : Exception
{
    public DynamicQueryException(string message) : base(message)
    {
    }
    
    public DynamicQueryException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// 字段路径无效异常
/// </summary>
public class InvalidFieldPathException : DynamicQueryException
{
    public string FieldPath { get; }
    
    public InvalidFieldPathException(string fieldPath) 
        : base($"Invalid field path: {fieldPath}")
    {
        FieldPath = fieldPath;
    }
}

/// <summary>
/// 操作符无效异常
/// </summary>
public class InvalidOperatorException : DynamicQueryException
{
    public string Operator { get; }
    
    public InvalidOperatorException(string @operator) 
        : base($"Invalid operator: {@operator}")
    {
        Operator = @operator;
    }
}

/// <summary>
/// Join深度超限异常
/// </summary>
public class JoinDepthExceededException : DynamicQueryException
{
    public int MaxDepth { get; }
    public int ActualDepth { get; }
    
    public JoinDepthExceededException(int maxDepth, int actualDepth) 
        : base($"Join depth exceeded. Max: {maxDepth}, Actual: {actualDepth}")
    {
        MaxDepth = maxDepth;
        ActualDepth = actualDepth;
    }
}
