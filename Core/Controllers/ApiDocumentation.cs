using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// API文档和示例
/// </summary>
public static class ApiDocumentation
{
    /// <summary>
    /// 获取API使用示例
    /// </summary>
    /// <returns>API使用示例</returns>
    public static ApiExamples GetExamples()
    {
        return new ApiExamples
        {
            ListExamples = GetListExamples(),
            FilterExamples = GetFilterExamples(),
            SearchExamples = GetSearchExamples(),
            OrderingExamples = GetOrderingExamples(),
            PaginationExamples = GetPaginationExamples(),
            ResponseExamples = GetResponseExamples()
        };
    }

    /// <summary>
    /// 获取列表查询示例
    /// </summary>
    /// <returns>列表查询示例</returns>
    private static List<ApiExample> GetListExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "获取所有记录",
                Description = "获取所有记录，使用默认分页",
                Method = "GET",
                Url = "/api/users",
                Response = """
                {
                  "success": true,
                  "data": [...],
                  "total": 100,
                  "page": 1,
                  "page_size": 20,
                  "has_next": true,
                  "has_previous": false
                }
                """
            },
            new()
            {
                Title = "获取单个记录",
                Description = "根据ID获取单个记录",
                Method = "GET",
                Url = "/api/users/123",
                Response = """
                {
                  "success": true,
                  "data": [
                    {
                      "id": 123,
                      "name": "张三",
                      "email": "<EMAIL>"
                    }
                  ],
                  "total": 1
                }
                """
            }
        };
    }

    /// <summary>
    /// 获取过滤示例
    /// </summary>
    /// <returns>过滤示例</returns>
    private static List<ApiExample> GetFilterExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "精确匹配过滤",
                Description = "根据字段精确值过滤",
                Method = "GET",
                Url = "/api/users?name=张三&age=25",
                Response = "返回name为'张三'且age为25的用户"
            },
            new()
            {
                Title = "模糊匹配过滤",
                Description = "使用icontains操作符进行不区分大小写的模糊匹配",
                Method = "GET",
                Url = "/api/users?name__icontains=张",
                Response = "返回name包含'张'的用户（不区分大小写）"
            },
            new()
            {
                Title = "范围过滤",
                Description = "使用gte和lte操作符进行范围过滤",
                Method = "GET",
                Url = "/api/users?age__gte=18&age__lte=65",
                Response = "返回年龄在18-65岁之间的用户"
            },
            new()
            {
                Title = "关联字段过滤",
                Description = "通过关联实体的字段进行过滤",
                Method = "GET",
                Url = "/api/posts?author__name__icontains=张&category__name=技术",
                Response = "返回作者名包含'张'且分类为'技术'的文章"
            },
            new()
            {
                Title = "列表过滤",
                Description = "使用in操作符进行列表过滤",
                Method = "GET",
                Url = "/api/users?status__in=active,pending",
                Response = "返回状态为active或pending的用户"
            },
            new()
            {
                Title = "空值过滤",
                Description = "使用isnull操作符过滤空值",
                Method = "GET",
                Url = "/api/users?deleted_at__isnull=true",
                Response = "返回未删除的用户（deleted_at为空）"
            }
        };
    }

    /// <summary>
    /// 获取搜索示例
    /// </summary>
    /// <returns>搜索示例</returns>
    private static List<ApiExample> GetSearchExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "全文搜索",
                Description = "在配置的搜索字段中搜索关键词",
                Method = "GET",
                Url = "/api/users?search=张三",
                Response = "在name、email等字段中搜索包含'张三'的用户"
            },
            new()
            {
                Title = "组合搜索和过滤",
                Description = "同时使用搜索和过滤条件",
                Method = "GET",
                Url = "/api/users?search=张&status=active",
                Response = "搜索包含'张'且状态为active的用户"
            }
        };
    }

    /// <summary>
    /// 获取排序示例
    /// </summary>
    /// <returns>排序示例</returns>
    private static List<ApiExample> GetOrderingExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "单字段排序",
                Description = "按单个字段升序排序",
                Method = "GET",
                Url = "/api/users?ordering=name",
                Response = "按name字段升序排序"
            },
            new()
            {
                Title = "降序排序",
                Description = "使用负号前缀进行降序排序",
                Method = "GET",
                Url = "/api/users?ordering=-created_at",
                Response = "按created_at字段降序排序"
            },
            new()
            {
                Title = "多字段排序",
                Description = "按多个字段排序，用逗号分隔",
                Method = "GET",
                Url = "/api/users?ordering=status,-created_at,name",
                Response = "先按status升序，再按created_at降序，最后按name升序"
            },
            new()
            {
                Title = "关联字段排序",
                Description = "按关联实体的字段排序",
                Method = "GET",
                Url = "/api/posts?ordering=-author__name,created_at",
                Response = "先按作者名降序，再按创建时间升序"
            }
        };
    }

    /// <summary>
    /// 获取分页示例
    /// </summary>
    /// <returns>分页示例</returns>
    private static List<ApiExample> GetPaginationExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "基础分页",
                Description = "指定页码和页大小",
                Method = "GET",
                Url = "/api/users?page=2&page_size=50",
                Response = "获取第2页，每页50条记录"
            },
            new()
            {
                Title = "只获取总数",
                Description = "只返回总记录数，不返回具体数据",
                Method = "GET",
                Url = "/api/users?count_only=true",
                Response = """
                {
                  "success": true,
                  "data": [],
                  "total": 1000,
                  "message": "总计 1000 条记录"
                }
                """
            }
        };
    }

    /// <summary>
    /// 获取响应示例
    /// </summary>
    /// <returns>响应示例</returns>
    private static List<ApiExample> GetResponseExamples()
    {
        return new List<ApiExample>
        {
            new()
            {
                Title = "成功响应",
                Description = "标准的成功响应格式",
                Response = """
                {
                  "success": true,
                  "data": [
                    {
                      "id": 1,
                      "name": "张三",
                      "email": "<EMAIL>",
                      "created_at": "2023-01-01T00:00:00Z"
                    }
                  ],
                  "total": 100,
                  "page": 1,
                  "page_size": 20,
                  "total_pages": 5,
                  "has_next": true,
                  "has_previous": false,
                  "timestamp": "2023-01-01T12:00:00Z"
                }
                """
            },
            new()
            {
                Title = "错误响应",
                Description = "标准的错误响应格式",
                Response = """
                {
                  "success": false,
                  "message": "请求参数验证失败",
                  "errors": [
                    "不允许的过滤字段: invalid_field",
                    "页大小不能超过 1000"
                  ],
                  "data": [],
                  "timestamp": "2023-01-01T12:00:00Z"
                }
                """
            },
            new()
            {
                Title = "调试响应",
                Description = "启用调试模式时的响应格式",
                Response = """
                {
                  "success": true,
                  "data": [...],
                  "total": 100,
                  "debug": {
                    "sql": "SELECT * FROM users WHERE name LIKE '%张%' ORDER BY created_at DESC LIMIT 20 OFFSET 0",
                    "parameters": "Filters: 1, Search: '张', Ordering: [-created_at], Page: 1, Size: 20"
                  }
                }
                """
            }
        };
    }

    /// <summary>
    /// 获取支持的过滤操作符文档
    /// </summary>
    /// <returns>操作符文档</returns>
    public static List<OperatorDocumentation> GetSupportedOperators()
    {
        return new List<OperatorDocumentation>
        {
            new() { Name = "exact", Description = "精确匹配（默认）", Example = "name=张三", ApplicableTypes = "所有类型" },
            new() { Name = "icontains", Description = "不区分大小写包含", Example = "name__icontains=张", ApplicableTypes = "字符串" },
            new() { Name = "contains", Description = "区分大小写包含", Example = "name__contains=张", ApplicableTypes = "字符串" },
            new() { Name = "startswith", Description = "以...开始", Example = "name__startswith=张", ApplicableTypes = "字符串" },
            new() { Name = "endswith", Description = "以...结束", Example = "name__endswith=三", ApplicableTypes = "字符串" },
            new() { Name = "gte", Description = "大于等于", Example = "age__gte=18", ApplicableTypes = "数值、日期" },
            new() { Name = "lte", Description = "小于等于", Example = "age__lte=65", ApplicableTypes = "数值、日期" },
            new() { Name = "gt", Description = "大于", Example = "age__gt=17", ApplicableTypes = "数值、日期" },
            new() { Name = "lt", Description = "小于", Example = "age__lt=66", ApplicableTypes = "数值、日期" },
            new() { Name = "in", Description = "在列表中", Example = "status__in=active,pending", ApplicableTypes = "所有类型" },
            new() { Name = "notin", Description = "不在列表中", Example = "status__notin=deleted,banned", ApplicableTypes = "所有类型" },
            new() { Name = "isnull", Description = "为空", Example = "deleted_at__isnull=true", ApplicableTypes = "所有类型" },
            new() { Name = "isnotnull", Description = "不为空", Example = "deleted_at__isnotnull=true", ApplicableTypes = "所有类型" },
            new() { Name = "ne", Description = "不等于", Example = "status__ne=deleted", ApplicableTypes = "所有类型" }
        };
    }
}

/// <summary>
/// API示例集合
/// </summary>
public class ApiExamples
{
    public List<ApiExample> ListExamples { get; set; } = new();
    public List<ApiExample> FilterExamples { get; set; } = new();
    public List<ApiExample> SearchExamples { get; set; } = new();
    public List<ApiExample> OrderingExamples { get; set; } = new();
    public List<ApiExample> PaginationExamples { get; set; } = new();
    public List<ApiExample> ResponseExamples { get; set; } = new();
}

/// <summary>
/// API示例
/// </summary>
public class ApiExample
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Method { get; set; } = "GET";
    public string Url { get; set; } = string.Empty;
    public string Response { get; set; } = string.Empty;
}

/// <summary>
/// 操作符文档
/// </summary>
public class OperatorDocumentation
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Example { get; set; } = string.Empty;
    public string ApplicableTypes { get; set; } = string.Empty;
}
