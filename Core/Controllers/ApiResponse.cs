using System;
using System.Text.Json.Serialization;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// API响应基类
/// </summary>
public class ApiResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; } = true;

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string? Message { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    [JsonPropertyName("errors")]
    public List<string>? Errors { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 调试信息（仅在调试模式下返回）
    /// </summary>
    [JsonPropertyName("debug")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public object? Debug { get; set; }
}

/// <summary>
/// 泛型API响应类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T> : ApiResponse
{
    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonPropertyName("data")]
    public List<T>? Data { get; set; }

    /// <summary>
    /// 总记录数
    /// </summary>
    [JsonPropertyName("total")]
    public long Total { get; set; }

    /// <summary>
    /// 当前页码
    /// </summary>
    [JsonPropertyName("page")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int Page { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    [JsonPropertyName("page_size")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    [JsonPropertyName("total_pages")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)Total / PageSize) : 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [JsonPropertyName("has_next")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool HasNext { get; set; }

    /// <summary>
    /// 是否有上一页
    /// </summary>
    [JsonPropertyName("has_previous")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public bool HasPrevious { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="total">总数</param>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse<T> CreateSuccess(List<T>? data = null, long total = 0, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data ?? new List<T>(),
            Total = total,
            Message = message
        };
    }

    /// <summary>
    /// 创建成功响应（单个对象）
    /// </summary>
    /// <param name="item">单个对象</param>
    /// <param name="message">消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse<T> CreateSuccess(T item, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = new List<T> { item },
            Total = 1,
            Message = message
        };
    }

    /// <summary>
    /// 创建分页成功响应
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="total">总数</param>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="message">消息</param>
    /// <returns>分页成功响应</returns>
    public static ApiResponse<T> CreatePagedSuccess(
        List<T> data, 
        long total, 
        int page, 
        int pageSize, 
        string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Total = total,
            Page = page,
            PageSize = pageSize,
            HasNext = (page * pageSize) < total,
            HasPrevious = page > 1,
            Message = message
        };
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errors">错误详情</param>
    /// <returns>错误响应</returns>
    public static ApiResponse<T> CreateError(string message, List<string>? errors = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors,
            Data = new List<T>()
        };
    }

    /// <summary>
    /// 创建错误响应（单个错误）
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="error">错误详情</param>
    /// <returns>错误响应</returns>
    public static ApiResponse<T> CreateError(string message, string error)
    {
        return CreateError(message, new List<string> { error });
    }

    /// <summary>
    /// 创建验证错误响应
    /// </summary>
    /// <param name="validationErrors">验证错误</param>
    /// <returns>验证错误响应</returns>
    public static ApiResponse<T> CreateValidationError(Dictionary<string, string[]> validationErrors)
    {
        var errors = validationErrors
            .SelectMany(kvp => kvp.Value.Select(error => $"{kvp.Key}: {error}"))
            .ToList();

        return CreateError("验证失败", errors);
    }

    /// <summary>
    /// 创建未找到响应
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>未找到响应</returns>
    public static ApiResponse<T> CreateNotFound(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "未找到请求的资源",
            Data = new List<T>()
        };
    }

    /// <summary>
    /// 创建未授权响应
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>未授权响应</returns>
    public static ApiResponse<T> CreateUnauthorized(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "未授权访问",
            Data = new List<T>()
        };
    }

    /// <summary>
    /// 创建禁止访问响应
    /// </summary>
    /// <param name="message">消息</param>
    /// <returns>禁止访问响应</returns>
    public static ApiResponse<T> CreateForbidden(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message ?? "禁止访问",
            Data = new List<T>()
        };
    }
}

/// <summary>
/// 分页信息
/// </summary>
public class PaginationInfo
{
    /// <summary>
    /// 当前页码
    /// </summary>
    [JsonPropertyName("page")]
    public int Page { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    [JsonPropertyName("page_size")]
    public int PageSize { get; set; }

    /// <summary>
    /// 总记录数
    /// </summary>
    [JsonPropertyName("total")]
    public long Total { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    [JsonPropertyName("total_pages")]
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)Total / PageSize) : 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [JsonPropertyName("has_next")]
    public bool HasNext => (Page * PageSize) < Total;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    [JsonPropertyName("has_previous")]
    public bool HasPrevious => Page > 1;

    /// <summary>
    /// 下一页页码
    /// </summary>
    [JsonPropertyName("next_page")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? NextPage => HasNext ? Page + 1 : null;

    /// <summary>
    /// 上一页页码
    /// </summary>
    [JsonPropertyName("previous_page")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? PreviousPage => HasPrevious ? Page - 1 : null;

    /// <summary>
    /// 当前页起始记录索引
    /// </summary>
    [JsonPropertyName("start_index")]
    public long StartIndex => (Page - 1) * PageSize + 1;

    /// <summary>
    /// 当前页结束记录索引
    /// </summary>
    [JsonPropertyName("end_index")]
    public long EndIndex => Math.Min(Page * PageSize, Total);
}
