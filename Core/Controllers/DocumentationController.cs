using System;
using Microsoft.AspNetCore.Mvc;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// API文档控制器
/// </summary>
[ApiController]
[Route("api/docs")]
public class DocumentationController : ControllerBase
{
    /// <summary>
    /// 获取API使用示例
    /// </summary>
    /// <returns>API使用示例</returns>
    [HttpGet("examples")]
    public ActionResult<ApiExamples> GetExamples()
    {
        var examples = ApiDocumentation.GetExamples();
        return Ok(examples);
    }

    /// <summary>
    /// 获取支持的过滤操作符
    /// </summary>
    /// <returns>操作符列表</returns>
    [HttpGet("operators")]
    public ActionResult<List<OperatorDocumentation>> GetOperators()
    {
        var operators = ApiDocumentation.GetSupportedOperators();
        return Ok(operators);
    }

    /// <summary>
    /// 获取API概览
    /// </summary>
    /// <returns>API概览</returns>
    [HttpGet("overview")]
    public ActionResult<ApiOverview> GetOverview()
    {
        var overview = new ApiOverview
        {
            Title = "DynamicFreesql API",
            Version = "1.0.0",
            Description = "基于FreeSql的动态查询API，支持灵活的过滤、搜索、排序和分页功能",
            Features = new List<string>
            {
                "动态过滤：支持14种过滤操作符",
                "关联查询：支持多级关联字段过滤和排序",
                "全文搜索：在多个字段中搜索关键词",
                "灵活排序：支持多字段排序",
                "分页查询：支持页码和页大小控制",
                "类型安全：自动类型转换和验证",
                "性能优化：智能Join规划和缓存",
                "安全防护：字段白名单和深度限制"
            },
            Endpoints = new List<EndpointInfo>
            {
                new()
                {
                    Method = "GET",
                    Path = "/api/{controller}",
                    Description = "获取实体列表",
                    Parameters = new List<ParameterInfo>
                    {
                        new() { Name = "page", Type = "int", Description = "页码（从1开始）", Default = "1" },
                        new() { Name = "page_size", Type = "int", Description = "页大小", Default = "20" },
                        new() { Name = "search", Type = "string", Description = "搜索关键词", Required = false },
                        new() { Name = "ordering", Type = "string[]", Description = "排序字段", Required = false },
                        new() { Name = "count_only", Type = "bool", Description = "只返回总数", Default = "false" },
                        new() { Name = "{field}__{operator}", Type = "any", Description = "动态过滤参数", Required = false }
                    }
                },
                new()
                {
                    Method = "GET",
                    Path = "/api/{controller}/{id}",
                    Description = "根据ID获取单个实体",
                    Parameters = new List<ParameterInfo>
                    {
                        new() { Name = "id", Type = "any", Description = "实体ID", Required = true }
                    }
                }
            }
        };

        return Ok(overview);
    }

    /// <summary>
    /// 获取查询参数说明
    /// </summary>
    /// <returns>查询参数说明</returns>
    [HttpGet("parameters")]
    public ActionResult<QueryParametersDocumentation> GetParameters()
    {
        var documentation = new QueryParametersDocumentation
        {
            FilterParameters = new FilterParametersDoc
            {
                Description = "过滤参数使用字段名加操作符的格式：{field}__{operator}={value}",
                Examples = new List<string>
                {
                    "name=张三 - 精确匹配",
                    "name__icontains=张 - 不区分大小写包含",
                    "age__gte=18 - 大于等于",
                    "status__in=active,pending - 在列表中",
                    "author__name__icontains=张 - 关联字段过滤"
                },
                SupportedOperators = ApiDocumentation.GetSupportedOperators()
            },
            SearchParameters = new SearchParametersDoc
            {
                Description = "搜索参数在配置的搜索字段中查找包含关键词的记录",
                Parameter = "search",
                Examples = new List<string>
                {
                    "search=张三 - 在所有搜索字段中查找包含'张三'的记录"
                }
            },
            OrderingParameters = new OrderingParametersDoc
            {
                Description = "排序参数支持单字段或多字段排序，使用负号前缀表示降序",
                Parameter = "ordering",
                Examples = new List<string>
                {
                    "ordering=name - 按name升序",
                    "ordering=-created_at - 按created_at降序",
                    "ordering=status,-created_at,name - 多字段排序",
                    "ordering=-author__name - 按关联字段排序"
                }
            },
            PaginationParameters = new PaginationParametersDoc
            {
                Description = "分页参数控制返回结果的数量和位置",
                Parameters = new List<ParameterInfo>
                {
                    new() { Name = "page", Type = "int", Description = "页码（从1开始）", Default = "1" },
                    new() { Name = "page_size", Type = "int", Description = "页大小（1-1000）", Default = "20" },
                    new() { Name = "count_only", Type = "bool", Description = "只返回总数", Default = "false" }
                }
            }
        };

        return Ok(documentation);
    }
}

/// <summary>
/// API概览
/// </summary>
public class ApiOverview
{
    public string Title { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> Features { get; set; } = new();
    public List<EndpointInfo> Endpoints { get; set; } = new();
}

/// <summary>
/// 端点信息
/// </summary>
public class EndpointInfo
{
    public string Method { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<ParameterInfo> Parameters { get; set; } = new();
}

/// <summary>
/// 参数信息
/// </summary>
public class ParameterInfo
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Default { get; set; }
    public bool Required { get; set; } = false;
}

/// <summary>
/// 查询参数文档
/// </summary>
public class QueryParametersDocumentation
{
    public FilterParametersDoc FilterParameters { get; set; } = new();
    public SearchParametersDoc SearchParameters { get; set; } = new();
    public OrderingParametersDoc OrderingParameters { get; set; } = new();
    public PaginationParametersDoc PaginationParameters { get; set; } = new();
}

/// <summary>
/// 过滤参数文档
/// </summary>
public class FilterParametersDoc
{
    public string Description { get; set; } = string.Empty;
    public List<string> Examples { get; set; } = new();
    public List<OperatorDocumentation> SupportedOperators { get; set; } = new();
}

/// <summary>
/// 搜索参数文档
/// </summary>
public class SearchParametersDoc
{
    public string Description { get; set; } = string.Empty;
    public string Parameter { get; set; } = string.Empty;
    public List<string> Examples { get; set; } = new();
}

/// <summary>
/// 排序参数文档
/// </summary>
public class OrderingParametersDoc
{
    public string Description { get; set; } = string.Empty;
    public string Parameter { get; set; } = string.Empty;
    public List<string> Examples { get; set; } = new();
}

/// <summary>
/// 分页参数文档
/// </summary>
public class PaginationParametersDoc
{
    public string Description { get; set; } = string.Empty;
    public List<ParameterInfo> Parameters { get; set; } = new();
}
