using System.Linq.Expressions;
using DynamicFreesql.Core.Query;
using DynamicFreesql.Core.Expressions;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 过滤器配置
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class FilterConfiguration<TEntity> where TEntity : class
{
    private readonly Dictionary<string, FilterFieldConfig> _fieldConfigs = new();
    private readonly IExpressionParser _expressionParser;

    public FilterConfiguration(IExpressionParser expressionParser)
    {
        _expressionParser = expressionParser ?? throw new ArgumentNullException(nameof(expressionParser));
    }

    /// <summary>
    /// 添加过滤字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <param name="allowedOperators">允许的操作符</param>
    /// <param name="fieldName">自定义字段名</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> AddField(
        Expression<Func<TEntity, object>> expression,
        FilterOperator[]? allowedOperators = null,
        string? fieldName = null)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        var name = fieldName ?? path;

        _fieldConfigs[name] = new FilterFieldConfig
        {
            FieldPath = path,
            Expression = expression,
            AllowedOperators = allowedOperators ?? GetDefaultOperators(),
            IsEnabled = true
        };

        return this;
    }

    /// <summary>
    /// 添加多个过滤字段
    /// </summary>
    /// <param name="expressions">字段表达式数组</param>
    /// <param name="allowedOperators">允许的操作符</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> AddFields(
        Expression<Func<TEntity, object>>[] expressions,
        FilterOperator[]? allowedOperators = null)
    {
        foreach (var expression in expressions)
        {
            AddField(expression, allowedOperators);
        }
        return this;
    }

    /// <summary>
    /// 移除过滤字段
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> RemoveField(string fieldName)
    {
        _fieldConfigs.Remove(fieldName);
        return this;
    }

    /// <summary>
    /// 禁用过滤字段
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> DisableField(string fieldName)
    {
        if (_fieldConfigs.TryGetValue(fieldName, out var config))
        {
            config.IsEnabled = false;
        }
        return this;
    }

    /// <summary>
    /// 启用过滤字段
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> EnableField(string fieldName)
    {
        if (_fieldConfigs.TryGetValue(fieldName, out var config))
        {
            config.IsEnabled = true;
        }
        return this;
    }

    /// <summary>
    /// 设置字段的允许操作符
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <param name="allowedOperators">允许的操作符</param>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> SetFieldOperators(string fieldName, FilterOperator[] allowedOperators)
    {
        if (_fieldConfigs.TryGetValue(fieldName, out var config))
        {
            config.AllowedOperators = allowedOperators;
        }
        return this;
    }

    /// <summary>
    /// 获取字段配置
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>字段配置</returns>
    public FilterFieldConfig? GetFieldConfig(string fieldName)
    {
        return _fieldConfigs.TryGetValue(fieldName, out var config) ? config : null;
    }

    /// <summary>
    /// 获取所有字段配置
    /// </summary>
    /// <returns>字段配置字典</returns>
    public IReadOnlyDictionary<string, FilterFieldConfig> GetAllFieldConfigs()
    {
        return _fieldConfigs.AsReadOnly();
    }

    /// <summary>
    /// 检查字段是否允许
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>是否允许</returns>
    public bool IsFieldAllowed(string fieldName)
    {
        return _fieldConfigs.TryGetValue(fieldName, out var config) && config.IsEnabled;
    }

    /// <summary>
    /// 检查字段操作符是否允许
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <param name="operator">操作符</param>
    /// <returns>是否允许</returns>
    public bool IsOperatorAllowed(string fieldName, FilterOperator @operator)
    {
        if (!_fieldConfigs.TryGetValue(fieldName, out var config) || !config.IsEnabled)
        {
            return false;
        }

        return config.AllowedOperators.Contains(@operator);
    }

    /// <summary>
    /// 获取字段的允许操作符
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>允许的操作符数组</returns>
    public FilterOperator[] GetAllowedOperators(string fieldName)
    {
        return _fieldConfigs.TryGetValue(fieldName, out var config) 
            ? config.AllowedOperators 
            : Array.Empty<FilterOperator>();
    }

    /// <summary>
    /// 清空所有配置
    /// </summary>
    /// <returns>配置对象</returns>
    public FilterConfiguration<TEntity> Clear()
    {
        _fieldConfigs.Clear();
        return this;
    }

    /// <summary>
    /// 获取配置统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public FilterConfigurationStatistics GetStatistics()
    {
        return new FilterConfigurationStatistics
        {
            TotalFields = _fieldConfigs.Count,
            EnabledFields = _fieldConfigs.Values.Count(c => c.IsEnabled),
            DisabledFields = _fieldConfigs.Values.Count(c => !c.IsEnabled),
            FieldsByOperatorCount = _fieldConfigs.Values
                .GroupBy(c => c.AllowedOperators.Length)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    /// <summary>
    /// 获取默认操作符
    /// </summary>
    /// <returns>默认操作符数组</returns>
    private static FilterOperator[] GetDefaultOperators()
    {
        return new[]
        {
            FilterOperator.Exact,
            FilterOperator.IContains,
            FilterOperator.Contains,
            FilterOperator.StartsWith,
            FilterOperator.EndsWith,
            FilterOperator.Gte,
            FilterOperator.Lte,
            FilterOperator.Gt,
            FilterOperator.Lt,
            FilterOperator.In,
            FilterOperator.NotIn,
            FilterOperator.IsNull,
            FilterOperator.IsNotNull,
            FilterOperator.NotEqual
        };
    }
}

/// <summary>
/// 过滤字段配置
/// </summary>
public class FilterFieldConfig
{
    /// <summary>
    /// 字段路径
    /// </summary>
    public string FieldPath { get; set; } = string.Empty;

    /// <summary>
    /// 字段表达式
    /// </summary>
    public LambdaExpression? Expression { get; set; }

    /// <summary>
    /// 允许的操作符
    /// </summary>
    public FilterOperator[] AllowedOperators { get; set; } = Array.Empty<FilterOperator>();

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 字段描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    public Type? FieldType { get; set; }

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public List<Func<object, bool>> ValidationRules { get; set; } = new();

    /// <summary>
    /// 添加验证规则
    /// </summary>
    /// <param name="rule">验证规则</param>
    /// <returns>字段配置</returns>
    public FilterFieldConfig AddValidationRule(Func<object, bool> rule)
    {
        ValidationRules.Add(rule);
        return this;
    }

    /// <summary>
    /// 验证值
    /// </summary>
    /// <param name="value">值</param>
    /// <returns>是否有效</returns>
    public bool ValidateValue(object value)
    {
        return ValidationRules.All(rule => rule(value));
    }
}

/// <summary>
/// 过滤配置统计信息
/// </summary>
public class FilterConfigurationStatistics
{
    /// <summary>
    /// 总字段数
    /// </summary>
    public int TotalFields { get; set; }

    /// <summary>
    /// 启用字段数
    /// </summary>
    public int EnabledFields { get; set; }

    /// <summary>
    /// 禁用字段数
    /// </summary>
    public int DisabledFields { get; set; }

    /// <summary>
    /// 按操作符数量分组的字段统计
    /// </summary>
    public Dictionary<int, int> FieldsByOperatorCount { get; set; } = new();
}

/// <summary>
/// 过滤器配置构建器
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class FilterConfigurationBuilder<TEntity> where TEntity : class
{
    private readonly FilterConfiguration<TEntity> _configuration;

    public FilterConfigurationBuilder(IExpressionParser expressionParser)
    {
        _configuration = new FilterConfiguration<TEntity>(expressionParser);
    }

    /// <summary>
    /// 配置字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>字段配置构建器</returns>
    public FilterFieldConfigurationBuilder<TEntity> Field(Expression<Func<TEntity, object>> expression)
    {
        return new FilterFieldConfigurationBuilder<TEntity>(_configuration, expression);
    }

    /// <summary>
    /// 构建配置
    /// </summary>
    /// <returns>过滤器配置</returns>
    public FilterConfiguration<TEntity> Build()
    {
        return _configuration;
    }
}

/// <summary>
/// 过滤字段配置构建器
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class FilterFieldConfigurationBuilder<TEntity> where TEntity : class
{
    private readonly FilterConfiguration<TEntity> _configuration;
    private readonly Expression<Func<TEntity, object>> _expression;
    private FilterOperator[]? _allowedOperators;
    private string? _fieldName;

    public FilterFieldConfigurationBuilder(
        FilterConfiguration<TEntity> configuration, 
        Expression<Func<TEntity, object>> expression)
    {
        _configuration = configuration;
        _expression = expression;
    }

    /// <summary>
    /// 设置允许的操作符
    /// </summary>
    /// <param name="operators">操作符数组</param>
    /// <returns>字段配置构建器</returns>
    public FilterFieldConfigurationBuilder<TEntity> WithOperators(params FilterOperator[] operators)
    {
        _allowedOperators = operators;
        return this;
    }

    /// <summary>
    /// 设置字段名
    /// </summary>
    /// <param name="name">字段名</param>
    /// <returns>字段配置构建器</returns>
    public FilterFieldConfigurationBuilder<TEntity> WithName(string name)
    {
        _fieldName = name;
        return this;
    }

    /// <summary>
    /// 添加字段到配置
    /// </summary>
    /// <returns>过滤器配置</returns>
    public FilterConfiguration<TEntity> Add()
    {
        _configuration.AddField(_expression, _allowedOperators, _fieldName);
        return _configuration;
    }
}
