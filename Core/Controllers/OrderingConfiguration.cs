using System.Linq.Expressions;
using DynamicFreesql.Core.Expressions;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 排序配置
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class OrderingConfiguration<TEntity> where TEntity : class
{
    private readonly List<OrderingFieldConfig> _fieldConfigs = new();
    private readonly IExpressionParser _expressionParser;

    /// <summary>
    /// 默认排序字段
    /// </summary>
    public string[] DefaultOrdering { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 最大排序字段数量
    /// </summary>
    public int MaxOrderingFields { get; set; } = 5;

    /// <summary>
    /// 是否启用排序
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    public OrderingConfiguration(IExpressionParser expressionParser)
    {
        _expressionParser = expressionParser ?? throw new ArgumentNullException(nameof(expressionParser));
    }

    /// <summary>
    /// 添加排序字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <param name="allowedDirections">允许的排序方向</param>
    /// <param name="defaultDirection">默认排序方向</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> AddField(
        Expression<Func<TEntity, object>> expression,
        OrderDirection[]? allowedDirections = null,
        OrderDirection defaultDirection = OrderDirection.Ascending)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        
        _fieldConfigs.Add(new OrderingFieldConfig
        {
            FieldPath = path,
            Expression = expression,
            AllowedDirections = allowedDirections ?? new[] { OrderDirection.Ascending, OrderDirection.Descending },
            DefaultDirection = defaultDirection,
            IsEnabled = true
        });

        return this;
    }

    /// <summary>
    /// 添加多个排序字段
    /// </summary>
    /// <param name="expressions">字段表达式数组</param>
    /// <param name="allowedDirections">允许的排序方向</param>
    /// <param name="defaultDirection">默认排序方向</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> AddFields(
        Expression<Func<TEntity, object>>[] expressions,
        OrderDirection[]? allowedDirections = null,
        OrderDirection defaultDirection = OrderDirection.Ascending)
    {
        foreach (var expression in expressions)
        {
            AddField(expression, allowedDirections, defaultDirection);
        }
        return this;
    }

    /// <summary>
    /// 移除排序字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> RemoveField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        _fieldConfigs.RemoveAll(c => c.FieldPath == path);
        return this;
    }

    /// <summary>
    /// 禁用排序字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> DisableField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == path);
        if (config != null)
        {
            config.IsEnabled = false;
        }
        return this;
    }

    /// <summary>
    /// 启用排序字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> EnableField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == path);
        if (config != null)
        {
            config.IsEnabled = true;
        }
        return this;
    }

    /// <summary>
    /// 设置默认排序
    /// </summary>
    /// <param name="ordering">默认排序字段</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> SetDefaultOrdering(params string[] ordering)
    {
        DefaultOrdering = ordering;
        return this;
    }

    /// <summary>
    /// 设置最大排序字段数量
    /// </summary>
    /// <param name="maxFields">最大字段数量</param>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> SetMaxFields(int maxFields)
    {
        MaxOrderingFields = maxFields;
        return this;
    }

    /// <summary>
    /// 检查字段是否允许排序
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>是否允许</returns>
    public bool IsFieldAllowed(string fieldPath)
    {
        if (!IsEnabled)
            return false;

        // 如果没有配置任何字段，则允许所有字段
        if (_fieldConfigs.Count == 0)
            return true;

        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == fieldPath);
        return config != null && config.IsEnabled;
    }

    /// <summary>
    /// 检查字段排序方向是否允许
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="direction">排序方向</param>
    /// <returns>是否允许</returns>
    public bool IsDirectionAllowed(string fieldPath, OrderDirection direction)
    {
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == fieldPath);
        if (config == null || !config.IsEnabled)
            return false;

        return config.AllowedDirections.Contains(direction);
    }

    /// <summary>
    /// 获取字段的默认排序方向
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>默认排序方向</returns>
    public OrderDirection GetDefaultDirection(string fieldPath)
    {
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == fieldPath);
        return config?.DefaultDirection ?? OrderDirection.Ascending;
    }

    /// <summary>
    /// 获取启用的排序字段
    /// </summary>
    /// <returns>启用的排序字段配置</returns>
    public IReadOnlyList<OrderingFieldConfig> GetEnabledFields()
    {
        return _fieldConfigs.Where(c => c.IsEnabled).ToList().AsReadOnly();
    }

    /// <summary>
    /// 获取排序字段路径
    /// </summary>
    /// <returns>排序字段路径数组</returns>
    public string[] GetFieldPaths()
    {
        return _fieldConfigs.Where(c => c.IsEnabled).Select(c => c.FieldPath).ToArray();
    }

    /// <summary>
    /// 验证排序参数
    /// </summary>
    /// <param name="ordering">排序参数</param>
    /// <returns>验证结果</returns>
    public OrderingValidationResult ValidateOrdering(string[] ordering)
    {
        var result = new OrderingValidationResult();

        if (!IsEnabled)
        {
            result.AddError("排序功能已禁用");
            return result;
        }

        if (ordering.Length > MaxOrderingFields)
        {
            result.AddError($"排序字段数量不能超过 {MaxOrderingFields} 个");
            return result;
        }

        foreach (var orderField in ordering)
        {
            var orderingPath = OrderingPath.Parse(orderField);
            if (!orderingPath.IsValid)
            {
                result.AddError($"无效的排序字段格式: {orderField}");
                continue;
            }

            if (!IsFieldAllowed(orderingPath.FieldPath))
            {
                result.AddError($"不允许的排序字段: {orderingPath.FieldPath}");
                continue;
            }

            if (!IsDirectionAllowed(orderingPath.FieldPath, orderingPath.Direction))
            {
                result.AddError($"字段 {orderingPath.FieldPath} 不允许 {orderingPath.Direction} 排序");
            }
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    /// <summary>
    /// 获取配置统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public OrderingConfigurationStatistics GetStatistics()
    {
        return new OrderingConfigurationStatistics
        {
            TotalFields = _fieldConfigs.Count,
            EnabledFields = _fieldConfigs.Count(c => c.IsEnabled),
            DisabledFields = _fieldConfigs.Count(c => !c.IsEnabled),
            DefaultOrderingFields = DefaultOrdering.Length,
            MaxOrderingFields = MaxOrderingFields,
            IsEnabled = IsEnabled,
            DirectionDistribution = _fieldConfigs
                .SelectMany(c => c.AllowedDirections)
                .GroupBy(d => d)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    /// <summary>
    /// 清空所有配置
    /// </summary>
    /// <returns>配置对象</returns>
    public OrderingConfiguration<TEntity> Clear()
    {
        _fieldConfigs.Clear();
        DefaultOrdering = Array.Empty<string>();
        return this;
    }
}

/// <summary>
/// 排序字段配置
/// </summary>
public class OrderingFieldConfig
{
    /// <summary>
    /// 字段路径
    /// </summary>
    public string FieldPath { get; set; } = string.Empty;

    /// <summary>
    /// 字段表达式
    /// </summary>
    public LambdaExpression? Expression { get; set; }

    /// <summary>
    /// 允许的排序方向
    /// </summary>
    public OrderDirection[] AllowedDirections { get; set; } = Array.Empty<OrderDirection>();

    /// <summary>
    /// 默认排序方向
    /// </summary>
    public OrderDirection DefaultDirection { get; set; } = OrderDirection.Ascending;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 字段描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 排序优先级
    /// </summary>
    public int Priority { get; set; } = 0;
}

/// <summary>
/// 排序验证结果
/// </summary>
public class OrderingValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 添加错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }
}

/// <summary>
/// 排序配置统计信息
/// </summary>
public class OrderingConfigurationStatistics
{
    /// <summary>
    /// 总字段数
    /// </summary>
    public int TotalFields { get; set; }

    /// <summary>
    /// 启用字段数
    /// </summary>
    public int EnabledFields { get; set; }

    /// <summary>
    /// 禁用字段数
    /// </summary>
    public int DisabledFields { get; set; }

    /// <summary>
    /// 默认排序字段数
    /// </summary>
    public int DefaultOrderingFields { get; set; }

    /// <summary>
    /// 最大排序字段数
    /// </summary>
    public int MaxOrderingFields { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 排序方向分布
    /// </summary>
    public Dictionary<OrderDirection, int> DirectionDistribution { get; set; } = new();
}
