using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 查询参数数据结构
/// </summary>
public class QueryParams
{
    /// <summary>
    /// 过滤参数字典
    /// </summary>
    public Dictionary<string, object> Filters { get; set; } = new();

    /// <summary>
    /// 搜索词
    /// </summary>
    [FromQuery(Name = "search")]
    public string? Search { get; set; }

    /// <summary>
    /// 排序字段数组
    /// </summary>
    [FromQuery(Name = "ordering")]
    public string[] Ordering { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [FromQuery(Name = "page")]
    [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
    public int Page { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    [FromQuery(Name = "page_size")]
    [Range(1, 1000, ErrorMessage = "页大小必须在1-1000之间")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 是否只返回总数
    /// </summary>
    [FromQuery(Name = "count_only")]
    public bool CountOnly { get; set; } = false;

    /// <summary>
    /// 包含字段列表（用于字段选择）
    /// </summary>
    [FromQuery(Name = "fields")]
    public string[] Fields { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 排除字段列表
    /// </summary>
    [FromQuery(Name = "exclude")]
    public string[] Exclude { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 是否启用调试模式（返回生成的SQL）
    /// </summary>
    [FromQuery(Name = "debug")]
    public bool Debug { get; set; } = false;

    /// <summary>
    /// 自定义参数扩展
    /// </summary>
    public Dictionary<string, object> Extensions { get; set; } = new();

    /// <summary>
    /// 从查询字符串解析过滤参数
    /// </summary>
    /// <param name="queryCollection">查询字符串集合</param>
    public void ParseFiltersFromQuery(IQueryCollection queryCollection)
    {
        var reservedParams = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "search", "ordering", "page", "page_size", "count_only", 
            "fields", "exclude", "debug"
        };

        foreach (var param in queryCollection)
        {
            if (reservedParams.Contains(param.Key))
                continue;

            // 解析过滤参数
            var value = param.Value.Count == 1 ? param.Value[0] : param.Value.ToArray();
            if (value != null)
            {
                Filters[param.Key] = value;
            }
        }
    }

    /// <summary>
    /// 验证查询参数
    /// </summary>
    /// <returns>验证结果</returns>
    public QueryParamsValidationResult Validate()
    {
        var result = new QueryParamsValidationResult();

        // 验证页码
        if (Page < 1)
        {
            result.Errors.Add("页码必须大于0");
        }

        // 验证页大小
        if (PageSize < 1 || PageSize > 1000)
        {
            result.Errors.Add("页大小必须在1-1000之间");
        }

        // 验证排序字段格式
        foreach (var orderField in Ordering)
        {
            if (string.IsNullOrWhiteSpace(orderField))
            {
                result.Errors.Add("排序字段不能为空");
                continue;
            }

            if (!IsValidOrderingField(orderField))
            {
                result.Errors.Add($"无效的排序字段格式: {orderField}");
            }
        }

        // 验证字段名格式
        foreach (var field in Fields)
        {
            if (string.IsNullOrWhiteSpace(field))
            {
                result.Errors.Add("字段名不能为空");
                continue;
            }

            if (!IsValidFieldName(field))
            {
                result.Errors.Add($"无效的字段名格式: {field}");
            }
        }

        foreach (var field in Exclude)
        {
            if (string.IsNullOrWhiteSpace(field))
            {
                result.Errors.Add("排除字段名不能为空");
                continue;
            }

            if (!IsValidFieldName(field))
            {
                result.Errors.Add($"无效的排除字段名格式: {field}");
            }
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    /// <summary>
    /// 获取跳过的记录数
    /// </summary>
    /// <returns>跳过的记录数</returns>
    public int GetSkip()
    {
        return (Page - 1) * PageSize;
    }

    /// <summary>
    /// 检查是否有过滤条件
    /// </summary>
    /// <returns>是否有过滤条件</returns>
    public bool HasFilters()
    {
        return Filters.Count > 0 || !string.IsNullOrWhiteSpace(Search);
    }

    /// <summary>
    /// 检查是否有排序条件
    /// </summary>
    /// <returns>是否有排序条件</returns>
    public bool HasOrdering()
    {
        return Ordering.Length > 0;
    }

    /// <summary>
    /// 检查是否需要分页
    /// </summary>
    /// <returns>是否需要分页</returns>
    public bool NeedsPaging()
    {
        return Page > 1 || PageSize < int.MaxValue;
    }

    /// <summary>
    /// 克隆查询参数
    /// </summary>
    /// <returns>克隆的查询参数</returns>
    public QueryParams Clone()
    {
        return new QueryParams
        {
            Filters = new Dictionary<string, object>(Filters),
            Search = Search,
            Ordering = (string[])Ordering.Clone(),
            Page = Page,
            PageSize = PageSize,
            CountOnly = CountOnly,
            Fields = (string[])Fields.Clone(),
            Exclude = (string[])Exclude.Clone(),
            Debug = Debug,
            Extensions = new Dictionary<string, object>(Extensions)
        };
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var parts = new List<string>();

        if (HasFilters())
        {
            parts.Add($"Filters: {Filters.Count}");
        }

        if (!string.IsNullOrWhiteSpace(Search))
        {
            parts.Add($"Search: '{Search}'");
        }

        if (HasOrdering())
        {
            parts.Add($"Ordering: [{string.Join(", ", Ordering)}]");
        }

        parts.Add($"Page: {Page}, Size: {PageSize}");

        return string.Join(", ", parts);
    }

    /// <summary>
    /// 验证排序字段格式
    /// </summary>
    /// <param name="field">排序字段</param>
    /// <returns>是否有效</returns>
    private static bool IsValidOrderingField(string field)
    {
        if (string.IsNullOrWhiteSpace(field))
            return false;

        // 移除排序方向前缀
        var fieldName = field.StartsWith('-') || field.StartsWith('+') ? field[1..] : field;
        
        return IsValidFieldName(fieldName);
    }

    /// <summary>
    /// 验证字段名格式
    /// </summary>
    /// <param name="fieldName">字段名</param>
    /// <returns>是否有效</returns>
    private static bool IsValidFieldName(string fieldName)
    {
        if (string.IsNullOrWhiteSpace(fieldName))
            return false;

        // 检查是否包含无效字符
        var invalidChars = new[] { ' ', '\t', '\n', '\r', '/', '\\', '?', '*', '<', '>', '|', '"', '\'' };
        if (fieldName.Any(c => invalidChars.Contains(c)))
            return false;

        // 检查是否以双下划线开头或结尾
        if (fieldName.StartsWith("__") || fieldName.EndsWith("__"))
            return false;

        // 检查是否包含连续的三个或更多下划线
        if (fieldName.Contains("___"))
            return false;

        return true;
    }
}

/// <summary>
/// 查询参数验证结果
/// </summary>
public class QueryParamsValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 添加错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    /// <summary>
    /// 添加警告信息
    /// </summary>
    /// <param name="warning">警告信息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    /// <summary>
    /// 获取所有错误信息的字符串表示
    /// </summary>
    /// <returns>错误信息字符串</returns>
    public string GetErrorsString()
    {
        return string.Join("; ", Errors);
    }
}
