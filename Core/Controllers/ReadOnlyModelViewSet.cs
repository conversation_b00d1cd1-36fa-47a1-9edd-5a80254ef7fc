using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using FreeSql;
using DynamicFreesql.Core.Query;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Common;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 只读模型视图集基类
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
/// <typeparam name="TKey">主键类型</typeparam>
[ApiController]
[Route("api/[controller]")]
public abstract class ReadOnlyModelViewSet<TEntity, TKey> : ControllerBase 
    where TEntity : class
    where TKey : struct
{
    protected readonly IFreeSql FreeSql;
    protected readonly IQueryBuilder<TEntity> QueryBuilder;
    protected readonly IModelMetadata ModelMetadata;
    protected readonly PathValidator PathValidator;
    protected readonly ILogger<ReadOnlyModelViewSet<TEntity, TKey>> Logger;
    protected readonly DynamicFreesqlOptions Options;

    /// <summary>
    /// 过滤器配置
    /// </summary>
    protected virtual FilterConfiguration<TEntity>? FilterConfiguration { get; }

    /// <summary>
    /// 搜索配置
    /// </summary>
    protected virtual SearchConfiguration<TEntity>? SearchConfiguration { get; }

    /// <summary>
    /// 排序配置
    /// </summary>
    protected virtual OrderingConfiguration<TEntity>? OrderingConfiguration { get; }

    /// <summary>
    /// 默认排序
    /// </summary>
    protected virtual string[] DefaultOrdering { get; } = Array.Empty<string>();

    /// <summary>
    /// 最大页大小
    /// </summary>
    protected virtual int MaxPageSize { get; } = 1000;

    /// <summary>
    /// 默认页大小
    /// </summary>
    protected virtual int DefaultPageSize { get; } = 20;

    /// <summary>
    /// 是否启用调试模式
    /// </summary>
    protected virtual bool EnableDebugMode { get; } = false;

    protected ReadOnlyModelViewSet(
        IFreeSql freeSql,
        IQueryBuilder<TEntity> queryBuilder,
        IModelMetadata modelMetadata,
        PathValidator pathValidator,
        ILogger<ReadOnlyModelViewSet<TEntity, TKey>> logger,
        DynamicFreesqlOptions options)
    {
        FreeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        QueryBuilder = queryBuilder ?? throw new ArgumentNullException(nameof(queryBuilder));
        ModelMetadata = modelMetadata ?? throw new ArgumentNullException(nameof(modelMetadata));
        PathValidator = pathValidator ?? throw new ArgumentNullException(nameof(pathValidator));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        Options = options ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 获取实体列表
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <returns>分页结果</returns>
    [HttpGet]
    public virtual async Task<ActionResult<ApiResponse<TEntity>>> List([FromQuery] QueryParams queryParams)
    {
        try
        {
            // 解析查询字符串中的过滤参数
            queryParams.ParseFiltersFromQuery(Request.Query);

            // 验证查询参数
            var validationResult = ValidateQueryParams(queryParams);
            if (!validationResult.IsValid)
            {
                return BadRequest(new ApiResponse<TEntity>
                {
                    Success = false,
                    Message = "查询参数验证失败",
                    Errors = validationResult.Errors
                });
            }

            // 如果只需要总数
            if (queryParams.CountOnly)
            {
                var count = await GetCountAsync(queryParams);
                return Ok(new ApiResponse<TEntity>
                {
                    Success = true,
                    Total = count,
                    Data = new List<TEntity>()
                });
            }

            // 构建查询
            var query = BuildQuery(queryParams);

            // 执行查询
            var (data, total) = await ExecuteQueryAsync(query, queryParams);

            // 构建响应
            var response = new ApiResponse<TEntity>
            {
                Success = true,
                Data = data,
                Total = total,
                Page = queryParams.Page,
                PageSize = queryParams.PageSize,
                HasNext = (queryParams.Page * queryParams.PageSize) < total,
                HasPrevious = queryParams.Page > 1
            };

            // 调试模式下添加SQL信息
            if (queryParams.Debug && EnableDebugMode)
            {
                try
                {
                    response.Debug = new
                    {
                        Sql = query.GetQuery().ToSql(),
                        Parameters = queryParams.ToString()
                    };
                }
                catch (Exception ex)
                {
                    response.Debug = new
                    {
                        Error = "无法获取SQL信息",
                        Message = ex.Message,
                        Parameters = queryParams.ToString()
                    };
                }
            }

            Logger.LogInformation("List query executed successfully. Returned {Count} items out of {Total}", 
                data.Count, total);

            return Ok(response);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing list query for entity {EntityType}", typeof(TEntity).Name);
            return StatusCode(500, new ApiResponse<TEntity>
            {
                Success = false,
                Message = "查询执行失败",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// 根据ID获取单个实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体详情</returns>
    [HttpGet("{id}")]
    public virtual async Task<ActionResult<ApiResponse<TEntity>>> Get(TKey id)
    {
        try
        {
            var entity = await GetByIdAsync(id);
            if (entity == null)
            {
                return NotFound(new ApiResponse<TEntity>
                {
                    Success = false,
                    Message = $"未找到ID为 {id} 的记录"
                });
            }

            Logger.LogInformation("Retrieved entity {EntityType} with ID {Id}", typeof(TEntity).Name, id);

            return Ok(new ApiResponse<TEntity>
            {
                Success = true,
                Data = new List<TEntity> { entity },
                Total = 1
            });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving entity {EntityType} with ID {Id}", typeof(TEntity).Name, id);
            return StatusCode(500, new ApiResponse<TEntity>
            {
                Success = false,
                Message = "获取记录失败",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    /// <summary>
    /// 验证查询参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <returns>验证结果</returns>
    protected virtual QueryParamsValidationResult ValidateQueryParams(QueryParams queryParams)
    {
        var result = queryParams.Validate();

        // 验证页大小限制
        if (queryParams.PageSize > MaxPageSize)
        {
            result.AddError($"页大小不能超过 {MaxPageSize}");
        }

        // 验证过滤字段
        foreach (var filter in queryParams.Filters)
        {
            if (!IsFilterFieldAllowed(filter.Key))
            {
                result.AddError($"不允许的过滤字段: {filter.Key}");
            }
        }

        // 验证排序字段
        foreach (var orderField in queryParams.Ordering)
        {
            var orderingPath = OrderingPath.Parse(orderField);
            if (!IsOrderingFieldAllowed(orderingPath.FieldPath))
            {
                result.AddError($"不允许的排序字段: {orderingPath.FieldPath}");
            }
        }

        return result;
    }

    /// <summary>
    /// 构建查询
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <returns>查询构建器</returns>
    protected virtual IQueryBuilder<TEntity> BuildQuery(QueryParams queryParams)
    {
        var query = QueryBuilder;

        // 应用过滤条件
        if (queryParams.HasFilters())
        {
            query = query.ApplyFilters(queryParams.Filters);
        }

        // 应用搜索条件
        if (!string.IsNullOrWhiteSpace(queryParams.Search))
        {
            var searchFieldPaths = GetSearchFieldPaths();
            query = query.ApplySearch(queryParams.Search, searchFieldPaths);
        }

        // 应用排序条件
        var ordering = queryParams.HasOrdering() ? queryParams.Ordering : GetDefaultOrdering();
        if (ordering.Length > 0)
        {
            query = query.ApplyOrdering(ordering);
        }

        // 应用分页
        if (queryParams.NeedsPaging())
        {
            query = query.ApplyPaging(queryParams.Page, queryParams.PageSize);
        }

        return query;
    }

    /// <summary>
    /// 执行查询
    /// </summary>
    /// <param name="query">查询构建器</param>
    /// <param name="queryParams">查询参数</param>
    /// <returns>数据和总数</returns>
    protected virtual async Task<(List<TEntity> Data, long Total)> ExecuteQueryAsync(
        IQueryBuilder<TEntity> query, 
        QueryParams queryParams)
    {
        // 获取总数（在应用分页之前）
        var countQuery = BuildQuery(queryParams);
        var total = await countQuery.CountAsync();

        // 获取数据
        var data = await query.ToListAsync();

        return (data, total);
    }

    /// <summary>
    /// 根据ID获取实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体</returns>
    protected virtual async Task<TEntity?> GetByIdAsync(TKey id)
    {
        return await FreeSql.Select<TEntity>()
            .Where($"Id = {{0}}", id)
            .FirstAsync();
    }

    /// <summary>
    /// 获取总数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <returns>总数</returns>
    protected virtual async Task<long> GetCountAsync(QueryParams queryParams)
    {
        var query = QueryBuilder;

        // 应用过滤条件
        if (queryParams.HasFilters())
        {
            query = query.ApplyFilters(queryParams.Filters);
        }

        // 应用搜索条件
        if (!string.IsNullOrWhiteSpace(queryParams.Search))
        {
            var searchFieldPaths = GetSearchFieldPaths();
            query = query.ApplySearch(queryParams.Search, searchFieldPaths);
        }

        return await query.CountAsync();
    }

    /// <summary>
    /// 检查过滤字段是否允许
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>是否允许</returns>
    protected virtual bool IsFilterFieldAllowed(string fieldPath)
    {
        if (FilterConfiguration != null)
        {
            return FilterConfiguration.IsFieldAllowed(fieldPath);
        }

        // 如果没有配置过滤器，则检查字段是否存在于实体中
        return ModelMetadata.IsValidFieldPath<TEntity>(fieldPath);
    }

    /// <summary>
    /// 检查排序字段是否允许
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>是否允许</returns>
    protected virtual bool IsOrderingFieldAllowed(string fieldPath)
    {
        if (OrderingConfiguration != null)
        {
            return OrderingConfiguration.IsFieldAllowed(fieldPath);
        }

        // 如果没有配置排序器，则检查字段是否存在于实体中
        return ModelMetadata.IsValidFieldPath<TEntity>(fieldPath);
    }

    /// <summary>
    /// 获取搜索字段路径
    /// </summary>
    /// <returns>搜索字段路径数组</returns>
    protected virtual string[] GetSearchFieldPaths()
    {
        if (SearchConfiguration != null)
        {
            return SearchConfiguration.GetFieldPaths();
        }

        return Array.Empty<string>();
    }

    /// <summary>
    /// 获取默认排序
    /// </summary>
    /// <returns>默认排序数组</returns>
    protected virtual string[] GetDefaultOrdering()
    {
        if (OrderingConfiguration != null && OrderingConfiguration.DefaultOrdering.Length > 0)
        {
            return OrderingConfiguration.DefaultOrdering;
        }

        if (DefaultOrdering.Length > 0)
        {
            return DefaultOrdering;
        }

        // 使用主键作为默认排序
        var entityMeta = ModelMetadata.GetEntityMeta(typeof(TEntity));
        return entityMeta.PrimaryKeys.Count > 0
            ? entityMeta.PrimaryKeys.ToArray()
            : new[] { "Id" };
    }
}
