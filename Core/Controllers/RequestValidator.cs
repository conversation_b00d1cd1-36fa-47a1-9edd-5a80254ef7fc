using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 请求验证器
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class RequestValidator<TEntity> where TEntity : class
{
    private readonly IModelMetadata _modelMetadata;
    private readonly PathValidator _pathValidator;
    private readonly IPathParser _pathParser;
    private readonly ILogger<RequestValidator<TEntity>> _logger;
    private readonly DynamicFreesqlOptions _options;

    public RequestValidator(
        IModelMetadata modelMetadata,
        PathValidator pathValidator,
        IPathParser pathParser,
        ILogger<RequestValidator<TEntity>> logger,
        DynamicFreesqlOptions options)
    {
        _modelMetadata = modelMetadata;
        _pathValidator = pathValidator;
        _pathParser = pathParser;
        _logger = logger;
        _options = options;
    }

    /// <summary>
    /// 验证查询参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <param name="filterConfig">过滤器配置</param>
    /// <param name="searchConfig">搜索配置</param>
    /// <param name="orderingConfig">排序配置</param>
    /// <returns>验证结果</returns>
    public RequestValidationResult ValidateQueryParams(
        QueryParams queryParams,
        FilterConfiguration<TEntity>? filterConfig = null,
        SearchConfiguration<TEntity>? searchConfig = null,
        OrderingConfiguration<TEntity>? orderingConfig = null)
    {
        var result = new RequestValidationResult();

        try
        {
            // 基础参数验证
            var basicValidation = queryParams.Validate();
            if (!basicValidation.IsValid)
            {
                result.Errors.AddRange(basicValidation.Errors);
            }

            // 验证分页参数
            ValidatePagination(queryParams, result);

            // 验证过滤参数
            ValidateFilters(queryParams, filterConfig, result);

            // 验证搜索参数
            ValidateSearch(queryParams, searchConfig, result);

            // 验证排序参数
            ValidateOrdering(queryParams, orderingConfig, result);

            result.IsValid = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating query parameters");
            result.Errors.Add($"验证过程中发生错误: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 验证分页参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <param name="result">验证结果</param>
    private void ValidatePagination(QueryParams queryParams, RequestValidationResult result)
    {
        if (queryParams.Page < 1)
        {
            result.Errors.Add("页码必须大于0");
        }

        if (queryParams.PageSize < 1)
        {
            result.Errors.Add("页大小必须大于0");
        }

        if (queryParams.PageSize > _options.MaxPageSize)
        {
            result.Errors.Add($"页大小不能超过 {_options.MaxPageSize}");
        }

        // 检查是否存在过大的跳过量（可能导致性能问题）
        var skip = queryParams.GetSkip();
        if (skip > 100000) // 可配置的阈值
        {
            result.Warnings.Add($"跳过记录数较大 ({skip})，可能影响查询性能");
        }
    }

    /// <summary>
    /// 验证过滤参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <param name="filterConfig">过滤器配置</param>
    /// <param name="result">验证结果</param>
    private void ValidateFilters(
        QueryParams queryParams, 
        FilterConfiguration<TEntity>? filterConfig, 
        RequestValidationResult result)
    {
        if (queryParams.Filters.Count == 0)
            return;

        foreach (var filter in queryParams.Filters)
        {
            try
            {
                // 解析过滤路径
                var filterPath = _pathParser.ParseFilterPath(filter.Key);
                
                // 验证字段路径
                var pathValidation = _pathValidator.ValidateFilterPath<TEntity>(filterPath);
                if (!pathValidation.IsValid)
                {
                    result.Errors.AddRange(pathValidation.Errors);
                    continue;
                }

                // 如果有过滤器配置，进行额外验证
                if (filterConfig != null)
                {
                    if (!filterConfig.IsFieldAllowed(filterPath.FieldPath))
                    {
                        result.Errors.Add($"不允许的过滤字段: {filterPath.FieldPath}");
                        continue;
                    }

                    if (!filterConfig.IsOperatorAllowed(filterPath.FieldPath, filterPath.Operator))
                    {
                        result.Errors.Add($"字段 {filterPath.FieldPath} 不允许使用操作符 {filterPath.Operator}");
                        continue;
                    }

                    // 验证字段值
                    var fieldConfig = filterConfig.GetFieldConfig(filterPath.FieldPath);
                    if (fieldConfig != null && !fieldConfig.ValidateValue(filter.Value))
                    {
                        result.Errors.Add($"字段 {filterPath.FieldPath} 的值验证失败");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to validate filter: {FilterKey}", filter.Key);
                result.Errors.Add($"过滤参数 {filter.Key} 格式错误");
            }
        }
    }

    /// <summary>
    /// 验证搜索参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <param name="searchConfig">搜索配置</param>
    /// <param name="result">验证结果</param>
    private void ValidateSearch(
        QueryParams queryParams, 
        SearchConfiguration<TEntity>? searchConfig, 
        RequestValidationResult result)
    {
        if (string.IsNullOrWhiteSpace(queryParams.Search))
            return;

        if (searchConfig != null)
        {
            var searchValidation = searchConfig.ValidateSearchTerm(queryParams.Search);
            if (!searchValidation.IsValid)
            {
                result.Errors.AddRange(searchValidation.Errors);
            }
        }
        else
        {
            // 基础搜索验证
            if (queryParams.Search.Length > 100)
            {
                result.Errors.Add("搜索词长度不能超过100个字符");
            }

            if (queryParams.Search.Length < 1)
            {
                result.Errors.Add("搜索词不能为空");
            }
        }
    }

    /// <summary>
    /// 验证排序参数
    /// </summary>
    /// <param name="queryParams">查询参数</param>
    /// <param name="orderingConfig">排序配置</param>
    /// <param name="result">验证结果</param>
    private void ValidateOrdering(
        QueryParams queryParams, 
        OrderingConfiguration<TEntity>? orderingConfig, 
        RequestValidationResult result)
    {
        if (queryParams.Ordering.Length == 0)
            return;

        if (orderingConfig != null)
        {
            var orderingValidation = orderingConfig.ValidateOrdering(queryParams.Ordering);
            if (!orderingValidation.IsValid)
            {
                result.Errors.AddRange(orderingValidation.Errors);
            }
        }
        else
        {
            // 基础排序验证
            if (queryParams.Ordering.Length > 5)
            {
                result.Errors.Add("排序字段数量不能超过5个");
            }

            foreach (var orderField in queryParams.Ordering)
            {
                var orderingPath = OrderingPath.Parse(orderField);
                if (!orderingPath.IsValid)
                {
                    result.Errors.Add($"无效的排序字段格式: {orderField}");
                    continue;
                }

                var pathValidation = _pathValidator.ValidateOrderingPath<TEntity>(orderingPath);
                if (!pathValidation.IsValid)
                {
                    result.Errors.AddRange(pathValidation.Errors);
                }
            }
        }
    }

    /// <summary>
    /// 验证字段选择参数
    /// </summary>
    /// <param name="fields">字段列表</param>
    /// <returns>验证结果</returns>
    public FieldSelectionValidationResult ValidateFieldSelection(string[] fields)
    {
        var result = new FieldSelectionValidationResult();

        if (fields.Length == 0)
        {
            result.IsValid = true;
            return result;
        }

        var entityMeta = _modelMetadata.GetEntityMeta(typeof(TEntity));

        foreach (var field in fields)
        {
            if (string.IsNullOrWhiteSpace(field))
            {
                result.Errors.Add("字段名不能为空");
                continue;
            }

            if (!_modelMetadata.IsValidFieldPath<TEntity>(field))
            {
                result.Errors.Add($"字段不存在: {field}");
                continue;
            }

            // 检查字段深度
            var segments = field.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
            if (segments.Length > _options.MaxJoinDepth)
            {
                result.Errors.Add($"字段路径深度超过限制: {field}");
            }
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    /// <summary>
    /// 验证单个过滤值
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="operator">操作符</param>
    /// <param name="value">值</param>
    /// <returns>验证结果</returns>
    public FilterValueValidationResult ValidateFilterValue(string fieldPath, string @operator, object value)
    {
        var result = new FilterValueValidationResult();

        try
        {
            var fieldType = _modelMetadata.GetFieldType(typeof(TEntity), fieldPath);
            if (fieldType == null)
            {
                result.Errors.Add($"字段不存在: {fieldPath}");
                return result;
            }

            var filterOperator = FilterPath.ParseOperator(@operator);
            if (!FilterPath.IsOperatorApplicableToType(filterOperator, fieldType))
            {
                result.Errors.Add($"操作符 {@operator} 不适用于字段类型 {fieldType.Name}");
                return result;
            }

            // 尝试类型转换
            try
            {
                var convertedValue = Convert.ChangeType(value, fieldType);
                result.ConvertedValue = convertedValue;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"值类型转换失败: {ex.Message}");
                return result;
            }

            result.IsValid = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating filter value for field {FieldPath}", fieldPath);
            result.Errors.Add($"验证过程中发生错误: {ex.Message}");
        }

        return result;
    }
}

/// <summary>
/// 请求验证结果
/// </summary>
public class RequestValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 字段选择验证结果
/// </summary>
public class FieldSelectionValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 有效字段列表
    /// </summary>
    public List<string> ValidFields { get; set; } = new();
}

/// <summary>
/// 过滤值验证结果
/// </summary>
public class FilterValueValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 转换后的值
    /// </summary>
    public object? ConvertedValue { get; set; }
}
