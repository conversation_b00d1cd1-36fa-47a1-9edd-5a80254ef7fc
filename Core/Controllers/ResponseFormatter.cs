using Microsoft.AspNetCore.Mvc;
using System.Text.Json;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 响应格式化器
/// </summary>
public class ResponseFormatter
{
    private readonly JsonSerializerOptions _jsonOptions;

    public ResponseFormatter(JsonSerializerOptions? jsonOptions = null)
    {
        _jsonOptions = jsonOptions ?? new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            WriteIndented = true,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    /// <summary>
    /// 格式化成功响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="data">数据</param>
    /// <param name="total">总数</param>
    /// <param name="queryParams">查询参数</param>
    /// <param name="message">消息</param>
    /// <returns>格式化的响应</returns>
    public ApiResponse<T> FormatSuccessResponse<T>(
        List<T> data,
        long total,
        QueryParams? queryParams = null,
        string? message = null)
    {
        var response = new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Total = total,
            Message = message,
            Timestamp = DateTime.UtcNow
        };

        if (queryParams != null)
        {
            response.Page = queryParams.Page;
            response.PageSize = queryParams.PageSize;
            response.HasNext = (queryParams.Page * queryParams.PageSize) < total;
            response.HasPrevious = queryParams.Page > 1;
        }

        return response;
    }

    /// <summary>
    /// 格式化单个实体响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="entity">实体</param>
    /// <param name="message">消息</param>
    /// <returns>格式化的响应</returns>
    public ApiResponse<T> FormatEntityResponse<T>(T entity, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = new List<T> { entity },
            Total = 1,
            Message = message,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 格式化错误响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="message">错误消息</param>
    /// <param name="errors">错误详情</param>
    /// <param name="statusCode">状态码</param>
    /// <returns>格式化的错误响应</returns>
    public ApiResponse<T> FormatErrorResponse<T>(
        string message,
        List<string>? errors = null,
        int statusCode = 500)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Errors = errors,
            Data = new List<T>(),
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 格式化验证错误响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="validationResult">验证结果</param>
    /// <returns>格式化的验证错误响应</returns>
    public ApiResponse<T> FormatValidationErrorResponse<T>(QueryParamsValidationResult validationResult)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = "请求参数验证失败",
            Errors = validationResult.Errors,
            Data = new List<T>(),
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 格式化分页响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="data">数据</param>
    /// <param name="pagination">分页信息</param>
    /// <param name="message">消息</param>
    /// <returns>格式化的分页响应</returns>
    public ApiResponse<T> FormatPagedResponse<T>(
        List<T> data,
        PaginationInfo pagination,
        string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data,
            Total = pagination.Total,
            Page = pagination.Page,
            PageSize = pagination.PageSize,
            HasNext = pagination.HasNext,
            HasPrevious = pagination.HasPrevious,
            Message = message,
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 格式化调试响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">基础响应</param>
    /// <param name="debugInfo">调试信息</param>
    /// <returns>包含调试信息的响应</returns>
    public ApiResponse<T> AddDebugInfo<T>(ApiResponse<T> response, object debugInfo)
    {
        response.Debug = debugInfo;
        return response;
    }

    /// <summary>
    /// 格式化计数响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="count">计数</param>
    /// <param name="message">消息</param>
    /// <returns>格式化的计数响应</returns>
    public ApiResponse<T> FormatCountResponse<T>(long count, string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = new List<T>(),
            Total = count,
            Message = message ?? $"总计 {count} 条记录",
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 格式化空响应
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="message">消息</param>
    /// <returns>格式化的空响应</returns>
    public ApiResponse<T> FormatEmptyResponse<T>(string? message = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = new List<T>(),
            Total = 0,
            Message = message ?? "没有找到匹配的记录",
            Timestamp = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建ActionResult
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <param name="statusCode">状态码</param>
    /// <returns>ActionResult</returns>
    public ActionResult<ApiResponse<T>> CreateActionResult<T>(
        ApiResponse<T> response,
        int? statusCode = null)
    {
        var code = statusCode ?? (response.Success ? 200 : 400);
        
        return new ObjectResult(response)
        {
            StatusCode = code
        };
    }

    /// <summary>
    /// 序列化响应为JSON
    /// </summary>
    /// <param name="response">响应对象</param>
    /// <returns>JSON字符串</returns>
    public string SerializeToJson(object response)
    {
        return JsonSerializer.Serialize(response, _jsonOptions);
    }

    /// <summary>
    /// 从JSON反序列化响应
    /// </summary>
    /// <typeparam name="T">响应类型</typeparam>
    /// <param name="json">JSON字符串</param>
    /// <returns>响应对象</returns>
    public T? DeserializeFromJson<T>(string json)
    {
        return JsonSerializer.Deserialize<T>(json, _jsonOptions);
    }
}

/// <summary>
/// 响应格式化器扩展方法
/// </summary>
public static class ResponseFormatterExtensions
{
    /// <summary>
    /// 转换为成功的ActionResult
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <returns>ActionResult</returns>
    public static ActionResult<ApiResponse<T>> ToOkResult<T>(this ApiResponse<T> response)
    {
        return new OkObjectResult(response);
    }

    /// <summary>
    /// 转换为错误的ActionResult
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <param name="statusCode">状态码</param>
    /// <returns>ActionResult</returns>
    public static ActionResult<ApiResponse<T>> ToErrorResult<T>(
        this ApiResponse<T> response,
        int statusCode = 400)
    {
        return new ObjectResult(response)
        {
            StatusCode = statusCode
        };
    }

    /// <summary>
    /// 转换为未找到的ActionResult
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <returns>ActionResult</returns>
    public static ActionResult<ApiResponse<T>> ToNotFoundResult<T>(this ApiResponse<T> response)
    {
        return new NotFoundObjectResult(response);
    }

    /// <summary>
    /// 转换为验证错误的ActionResult
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <returns>ActionResult</returns>
    public static ActionResult<ApiResponse<T>> ToBadRequestResult<T>(this ApiResponse<T> response)
    {
        return new BadRequestObjectResult(response);
    }

    /// <summary>
    /// 添加分页链接
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="response">响应对象</param>
    /// <param name="baseUrl">基础URL</param>
    /// <param name="queryParams">查询参数</param>
    /// <returns>响应对象</returns>
    public static ApiResponse<T> AddPaginationLinks<T>(
        this ApiResponse<T> response,
        string baseUrl,
        QueryParams queryParams)
    {
        var links = new Dictionary<string, string>();

        // 当前页链接
        links["self"] = BuildPageUrl(baseUrl, queryParams, queryParams.Page);

        // 第一页链接
        if (queryParams.Page > 1)
        {
            links["first"] = BuildPageUrl(baseUrl, queryParams, 1);
            links["prev"] = BuildPageUrl(baseUrl, queryParams, queryParams.Page - 1);
        }

        // 最后一页链接
        if (response.HasNext)
        {
            links["next"] = BuildPageUrl(baseUrl, queryParams, queryParams.Page + 1);
            
            if (response.TotalPages > 0)
            {
                links["last"] = BuildPageUrl(baseUrl, queryParams, response.TotalPages);
            }
        }

        // 将链接添加到调试信息中
        if (response.Debug is Dictionary<string, object> debugDict)
        {
            debugDict["links"] = links;
        }
        else
        {
            response.Debug = new { links };
        }

        return response;
    }

    /// <summary>
    /// 构建分页URL
    /// </summary>
    /// <param name="baseUrl">基础URL</param>
    /// <param name="queryParams">查询参数</param>
    /// <param name="page">页码</param>
    /// <returns>分页URL</returns>
    private static string BuildPageUrl(string baseUrl, QueryParams queryParams, int page)
    {
        var url = $"{baseUrl}?page={page}&page_size={queryParams.PageSize}";
        
        if (!string.IsNullOrWhiteSpace(queryParams.Search))
        {
            url += $"&search={Uri.EscapeDataString(queryParams.Search)}";
        }

        if (queryParams.Ordering.Length > 0)
        {
            url += $"&ordering={string.Join(",", queryParams.Ordering.Select(Uri.EscapeDataString))}";
        }

        foreach (var filter in queryParams.Filters)
        {
            url += $"&{Uri.EscapeDataString(filter.Key)}={Uri.EscapeDataString(filter.Value?.ToString() ?? "")}";
        }

        return url;
    }
}
