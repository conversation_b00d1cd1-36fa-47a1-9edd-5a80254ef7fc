using System.Linq.Expressions;
using DynamicFreesql.Core.Expressions;

namespace DynamicFreesql.Core.Controllers;

/// <summary>
/// 搜索配置
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class SearchConfiguration<TEntity> where TEntity : class
{
    private readonly List<SearchFieldConfig> _fieldConfigs = new();
    private readonly IExpressionParser _expressionParser;

    /// <summary>
    /// 搜索模式
    /// </summary>
    public SearchMode Mode { get; set; } = SearchMode.Any;

    /// <summary>
    /// 是否启用搜索
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 最小搜索长度
    /// </summary>
    public int MinSearchLength { get; set; } = 1;

    /// <summary>
    /// 最大搜索长度
    /// </summary>
    public int MaxSearchLength { get; set; } = 100;

    /// <summary>
    /// 是否区分大小写
    /// </summary>
    public bool CaseSensitive { get; set; } = false;

    public SearchConfiguration(IExpressionParser expressionParser)
    {
        _expressionParser = expressionParser ?? throw new ArgumentNullException(nameof(expressionParser));
    }

    /// <summary>
    /// 添加搜索字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <param name="weight">权重</param>
    /// <param name="searchType">搜索类型</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> AddField(
        Expression<Func<TEntity, object>> expression,
        double weight = 1.0,
        SearchType searchType = SearchType.Contains)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        
        _fieldConfigs.Add(new SearchFieldConfig
        {
            FieldPath = path,
            Expression = expression,
            Weight = weight,
            SearchType = searchType,
            IsEnabled = true
        });

        return this;
    }

    /// <summary>
    /// 添加多个搜索字段
    /// </summary>
    /// <param name="expressions">字段表达式数组</param>
    /// <param name="weight">权重</param>
    /// <param name="searchType">搜索类型</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> AddFields(
        Expression<Func<TEntity, object>>[] expressions,
        double weight = 1.0,
        SearchType searchType = SearchType.Contains)
    {
        foreach (var expression in expressions)
        {
            AddField(expression, weight, searchType);
        }
        return this;
    }

    /// <summary>
    /// 移除搜索字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> RemoveField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        _fieldConfigs.RemoveAll(c => c.FieldPath == path);
        return this;
    }

    /// <summary>
    /// 禁用搜索字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> DisableField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == path);
        if (config != null)
        {
            config.IsEnabled = false;
        }
        return this;
    }

    /// <summary>
    /// 启用搜索字段
    /// </summary>
    /// <param name="expression">字段表达式</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> EnableField(Expression<Func<TEntity, object>> expression)
    {
        var path = _expressionParser.GetPropertyPath(expression);
        var config = _fieldConfigs.FirstOrDefault(c => c.FieldPath == path);
        if (config != null)
        {
            config.IsEnabled = true;
        }
        return this;
    }

    /// <summary>
    /// 设置搜索模式
    /// </summary>
    /// <param name="mode">搜索模式</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> SetMode(SearchMode mode)
    {
        Mode = mode;
        return this;
    }

    /// <summary>
    /// 设置搜索长度限制
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> SetLengthLimits(int minLength, int maxLength)
    {
        MinSearchLength = minLength;
        MaxSearchLength = maxLength;
        return this;
    }

    /// <summary>
    /// 设置是否区分大小写
    /// </summary>
    /// <param name="caseSensitive">是否区分大小写</param>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> SetCaseSensitive(bool caseSensitive)
    {
        CaseSensitive = caseSensitive;
        return this;
    }

    /// <summary>
    /// 获取启用的搜索字段
    /// </summary>
    /// <returns>启用的搜索字段配置</returns>
    public IReadOnlyList<SearchFieldConfig> GetEnabledFields()
    {
        return _fieldConfigs.Where(c => c.IsEnabled).ToList().AsReadOnly();
    }

    /// <summary>
    /// 获取搜索字段路径
    /// </summary>
    /// <returns>搜索字段路径数组</returns>
    public string[] GetFieldPaths()
    {
        return _fieldConfigs.Where(c => c.IsEnabled).Select(c => c.FieldPath).ToArray();
    }

    /// <summary>
    /// 验证搜索词
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <returns>验证结果</returns>
    public SearchValidationResult ValidateSearchTerm(string? searchTerm)
    {
        var result = new SearchValidationResult();

        if (!IsEnabled)
        {
            result.AddError("搜索功能已禁用");
            return result;
        }

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            result.AddError("搜索词不能为空");
            return result;
        }

        if (searchTerm.Length < MinSearchLength)
        {
            result.AddError($"搜索词长度不能少于 {MinSearchLength} 个字符");
            return result;
        }

        if (searchTerm.Length > MaxSearchLength)
        {
            result.AddError($"搜索词长度不能超过 {MaxSearchLength} 个字符");
            return result;
        }

        if (_fieldConfigs.Count(c => c.IsEnabled) == 0)
        {
            result.AddError("没有配置搜索字段");
            return result;
        }

        result.IsValid = true;
        return result;
    }

    /// <summary>
    /// 获取配置统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public SearchConfigurationStatistics GetStatistics()
    {
        return new SearchConfigurationStatistics
        {
            TotalFields = _fieldConfigs.Count,
            EnabledFields = _fieldConfigs.Count(c => c.IsEnabled),
            DisabledFields = _fieldConfigs.Count(c => !c.IsEnabled),
            SearchTypeDistribution = _fieldConfigs
                .GroupBy(c => c.SearchType)
                .ToDictionary(g => g.Key, g => g.Count()),
            AverageWeight = _fieldConfigs.Count > 0 ? _fieldConfigs.Average(c => c.Weight) : 0,
            IsEnabled = IsEnabled,
            Mode = Mode
        };
    }

    /// <summary>
    /// 清空所有配置
    /// </summary>
    /// <returns>配置对象</returns>
    public SearchConfiguration<TEntity> Clear()
    {
        _fieldConfigs.Clear();
        return this;
    }
}

/// <summary>
/// 搜索字段配置
/// </summary>
public class SearchFieldConfig
{
    /// <summary>
    /// 字段路径
    /// </summary>
    public string FieldPath { get; set; } = string.Empty;

    /// <summary>
    /// 字段表达式
    /// </summary>
    public LambdaExpression? Expression { get; set; }

    /// <summary>
    /// 权重
    /// </summary>
    public double Weight { get; set; } = 1.0;

    /// <summary>
    /// 搜索类型
    /// </summary>
    public SearchType SearchType { get; set; } = SearchType.Contains;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 字段描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 搜索类型
/// </summary>
public enum SearchType
{
    /// <summary>
    /// 包含
    /// </summary>
    Contains,

    /// <summary>
    /// 不区分大小写包含
    /// </summary>
    IContains,

    /// <summary>
    /// 开始于
    /// </summary>
    StartsWith,

    /// <summary>
    /// 结束于
    /// </summary>
    EndsWith,

    /// <summary>
    /// 精确匹配
    /// </summary>
    Exact,

    /// <summary>
    /// 全文搜索
    /// </summary>
    FullText
}

/// <summary>
/// 搜索模式
/// </summary>
public enum SearchMode
{
    /// <summary>
    /// 任一字段匹配（OR）
    /// </summary>
    Any,

    /// <summary>
    /// 所有字段匹配（AND）
    /// </summary>
    All
}

/// <summary>
/// 搜索验证结果
/// </summary>
public class SearchValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 添加错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }
}

/// <summary>
/// 搜索配置统计信息
/// </summary>
public class SearchConfigurationStatistics
{
    /// <summary>
    /// 总字段数
    /// </summary>
    public int TotalFields { get; set; }

    /// <summary>
    /// 启用字段数
    /// </summary>
    public int EnabledFields { get; set; }

    /// <summary>
    /// 禁用字段数
    /// </summary>
    public int DisabledFields { get; set; }

    /// <summary>
    /// 搜索类型分布
    /// </summary>
    public Dictionary<SearchType, int> SearchTypeDistribution { get; set; } = new();

    /// <summary>
    /// 平均权重
    /// </summary>
    public double AverageWeight { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 搜索模式
    /// </summary>
    public SearchMode Mode { get; set; }
}
