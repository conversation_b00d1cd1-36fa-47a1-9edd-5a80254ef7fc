using System.Linq.Expressions;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Common;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 复杂表达式处理器
/// </summary>
public class ComplexExpressionHandler
{
    private readonly ILogger<ComplexExpressionHandler> _logger;

    public ComplexExpressionHandler(ILogger<ComplexExpressionHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 处理复杂表达式
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>处理结果</returns>
    public ExpressionProcessingResult ProcessExpression(Expression expression)
    {
        var result = new ExpressionProcessingResult
        {
            OriginalExpression = expression
        };

        try
        {
            var processedExpression = ProcessExpressionInternal(expression);
            result.ProcessedExpression = processedExpression;
            result.IsSuccess = true;

            _logger.LogDebug("Successfully processed complex expression of type {ExpressionType}", 
                expression.GetType().Name);
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to process complex expression of type {ExpressionType}", 
                expression.GetType().Name);
        }

        return result;
    }

    /// <summary>
    /// 内部处理表达式方法
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessExpressionInternal(Expression expression)
    {
        return expression switch
        {
            UnaryExpression unaryExpression => ProcessUnaryExpression(unaryExpression),
            MemberExpression memberExpression => ProcessMemberExpression(memberExpression),
            ParameterExpression parameterExpression => ProcessParameterExpression(parameterExpression),
            ConstantExpression constantExpression => ProcessConstantExpression(constantExpression),
            BinaryExpression binaryExpression => ProcessBinaryExpression(binaryExpression),
            ConditionalExpression conditionalExpression => ProcessConditionalExpression(conditionalExpression),
            MethodCallExpression methodCallExpression => ProcessMethodCallExpression(methodCallExpression),
            _ => expression
        };
    }

    /// <summary>
    /// 处理一元表达式
    /// </summary>
    /// <param name="unaryExpression">一元表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessUnaryExpression(UnaryExpression unaryExpression)
    {
        _logger.LogDebug("Processing unary expression: {NodeType}", unaryExpression.NodeType);

        return unaryExpression.NodeType switch
        {
            ExpressionType.Convert => ProcessConvertExpression(unaryExpression),
            ExpressionType.ConvertChecked => ProcessConvertCheckedExpression(unaryExpression),
            ExpressionType.TypeAs => ProcessTypeAsExpression(unaryExpression),
            ExpressionType.Not => ProcessNotExpression(unaryExpression),
            ExpressionType.Negate => ProcessNegateExpression(unaryExpression),
            ExpressionType.UnaryPlus => ProcessUnaryPlusExpression(unaryExpression),
            _ => unaryExpression
        };
    }

    /// <summary>
    /// 处理类型转换表达式
    /// </summary>
    /// <param name="convertExpression">转换表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessConvertExpression(UnaryExpression convertExpression)
    {
        var operand = ProcessExpressionInternal(convertExpression.Operand);
        
        // 如果转换是不必要的（例如从object到具体类型的装箱转换），直接返回操作数
        if (IsUnnecessaryConversion(convertExpression, operand))
        {
            _logger.LogDebug("Removed unnecessary conversion from {FromType} to {ToType}", 
                operand.Type.Name, convertExpression.Type.Name);
            return operand;
        }

        // 如果操作数发生了变化，创建新的转换表达式
        if (operand != convertExpression.Operand)
        {
            return Expression.Convert(operand, convertExpression.Type);
        }

        return convertExpression;
    }

    /// <summary>
    /// 处理检查转换表达式
    /// </summary>
    /// <param name="convertExpression">检查转换表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessConvertCheckedExpression(UnaryExpression convertExpression)
    {
        var operand = ProcessExpressionInternal(convertExpression.Operand);
        
        if (operand != convertExpression.Operand)
        {
            return Expression.ConvertChecked(operand, convertExpression.Type);
        }

        return convertExpression;
    }

    /// <summary>
    /// 处理TypeAs表达式
    /// </summary>
    /// <param name="typeAsExpression">TypeAs表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessTypeAsExpression(UnaryExpression typeAsExpression)
    {
        var operand = ProcessExpressionInternal(typeAsExpression.Operand);
        
        if (operand != typeAsExpression.Operand)
        {
            return Expression.TypeAs(operand, typeAsExpression.Type);
        }

        return typeAsExpression;
    }

    /// <summary>
    /// 处理Not表达式
    /// </summary>
    /// <param name="notExpression">Not表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessNotExpression(UnaryExpression notExpression)
    {
        var operand = ProcessExpressionInternal(notExpression.Operand);
        
        if (operand != notExpression.Operand)
        {
            return Expression.Not(operand);
        }

        return notExpression;
    }

    /// <summary>
    /// 处理取负表达式
    /// </summary>
    /// <param name="negateExpression">取负表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessNegateExpression(UnaryExpression negateExpression)
    {
        var operand = ProcessExpressionInternal(negateExpression.Operand);
        
        if (operand != negateExpression.Operand)
        {
            return Expression.Negate(operand);
        }

        return negateExpression;
    }

    /// <summary>
    /// 处理一元加表达式
    /// </summary>
    /// <param name="unaryPlusExpression">一元加表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessUnaryPlusExpression(UnaryExpression unaryPlusExpression)
    {
        // 一元加通常是多余的，可以直接返回操作数
        var operand = ProcessExpressionInternal(unaryPlusExpression.Operand);
        
        _logger.LogDebug("Simplified unary plus expression");
        return operand;
    }

    /// <summary>
    /// 处理成员表达式
    /// </summary>
    /// <param name="memberExpression">成员表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessMemberExpression(MemberExpression memberExpression)
    {
        if (memberExpression.Expression != null)
        {
            var processedExpression = ProcessExpressionInternal(memberExpression.Expression);
            
            if (processedExpression != memberExpression.Expression)
            {
                return Expression.MakeMemberAccess(processedExpression, memberExpression.Member);
            }
        }

        return memberExpression;
    }

    /// <summary>
    /// 处理参数表达式
    /// </summary>
    /// <param name="parameterExpression">参数表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessParameterExpression(ParameterExpression parameterExpression)
    {
        // 参数表达式通常不需要处理
        return parameterExpression;
    }

    /// <summary>
    /// 处理常量表达式
    /// </summary>
    /// <param name="constantExpression">常量表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessConstantExpression(ConstantExpression constantExpression)
    {
        // 常量表达式通常不需要处理
        return constantExpression;
    }

    /// <summary>
    /// 处理二元表达式
    /// </summary>
    /// <param name="binaryExpression">二元表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessBinaryExpression(BinaryExpression binaryExpression)
    {
        var left = ProcessExpressionInternal(binaryExpression.Left);
        var right = ProcessExpressionInternal(binaryExpression.Right);

        if (left != binaryExpression.Left || right != binaryExpression.Right)
        {
            return Expression.MakeBinary(binaryExpression.NodeType, left, right);
        }

        return binaryExpression;
    }

    /// <summary>
    /// 处理条件表达式
    /// </summary>
    /// <param name="conditionalExpression">条件表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessConditionalExpression(ConditionalExpression conditionalExpression)
    {
        var test = ProcessExpressionInternal(conditionalExpression.Test);
        var ifTrue = ProcessExpressionInternal(conditionalExpression.IfTrue);
        var ifFalse = ProcessExpressionInternal(conditionalExpression.IfFalse);

        if (test != conditionalExpression.Test || 
            ifTrue != conditionalExpression.IfTrue || 
            ifFalse != conditionalExpression.IfFalse)
        {
            return Expression.Condition(test, ifTrue, ifFalse);
        }

        return conditionalExpression;
    }

    /// <summary>
    /// 处理方法调用表达式
    /// </summary>
    /// <param name="methodCallExpression">方法调用表达式</param>
    /// <returns>处理后的表达式</returns>
    private Expression ProcessMethodCallExpression(MethodCallExpression methodCallExpression)
    {
        // 对于属性访问的方法调用，通常不需要特殊处理
        // 但可以在这里添加特定的优化逻辑
        return methodCallExpression;
    }

    /// <summary>
    /// 检查转换是否不必要
    /// </summary>
    /// <param name="convertExpression">转换表达式</param>
    /// <param name="operand">操作数</param>
    /// <returns>是否不必要</returns>
    private static bool IsUnnecessaryConversion(UnaryExpression convertExpression, Expression operand)
    {
        // 如果目标类型和操作数类型相同，转换是不必要的
        if (convertExpression.Type == operand.Type)
            return true;

        // 如果是从值类型到object的装箱，在属性访问上下文中可能是不必要的
        if (convertExpression.Type == typeof(object) && operand.Type.IsValueType)
            return true;

        // 如果是从可空类型到其基础类型的转换，在某些上下文中可能是不必要的
        var underlyingType = Nullable.GetUnderlyingType(operand.Type);
        if (underlyingType != null && underlyingType == convertExpression.Type)
            return false; // 这种转换通常是必要的

        return false;
    }
}

/// <summary>
/// 表达式处理结果
/// </summary>
public class ExpressionProcessingResult
{
    /// <summary>
    /// 原始表达式
    /// </summary>
    public Expression OriginalExpression { get; set; } = null!;

    /// <summary>
    /// 处理后的表达式
    /// </summary>
    public Expression? ProcessedExpression { get; set; }

    /// <summary>
    /// 是否处理成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 是否发生了变化
    /// </summary>
    public bool HasChanged => ProcessedExpression != null && ProcessedExpression != OriginalExpression;
}
