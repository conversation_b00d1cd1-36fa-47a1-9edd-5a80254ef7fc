using System.Linq.Expressions;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Common;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 表达式解析器实现类
/// </summary>
public class ExpressionParser : IExpressionParser
{
    private readonly ILogger<ExpressionParser> _logger;
    private readonly ComplexExpressionHandler _complexExpressionHandler;

    public ExpressionParser(
        ILogger<ExpressionParser> logger,
        ComplexExpressionHandler complexExpressionHandler)
    {
        _logger = logger;
        _complexExpressionHandler = complexExpressionHandler;
    }

    /// <inheritdoc />
    public string GetPropertyPath<TEntity>(Expression<Func<TEntity, object>> expression)
    {
        if (expression == null)
            throw new ArgumentNullException(nameof(expression));

        try
        {
            var path = ExtractPropertyPath(expression.Body);
            _logger.LogDebug("Extracted property path '{Path}' from expression for entity {EntityType}", 
                path, typeof(TEntity).Name);
            return path;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract property path from expression for entity {EntityType}", 
                typeof(TEntity).Name);
            throw new DynamicQueryException($"Invalid property expression for entity {typeof(TEntity).Name}", ex);
        }
    }

    /// <inheritdoc />
    public bool IsValidPropertyExpression<TEntity>(Expression<Func<TEntity, object>> expression)
    {
        if (expression == null)
            return false;

        try
        {
            ExtractPropertyPath(expression.Body);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 从表达式体中提取属性路径
    /// </summary>
    /// <param name="expression">表达式体</param>
    /// <returns>属性路径</returns>
    private string ExtractPropertyPath(Expression expression)
    {
        var parts = new List<string>();
        var current = expression;

        // 处理类型转换表达式
        current = UnwrapConvertExpression(current);

        // 递归提取成员访问路径
        while (current is MemberExpression memberExpression)
        {
            parts.Insert(0, memberExpression.Member.Name);
            current = memberExpression.Expression;
            
            // 处理嵌套的类型转换
            current = UnwrapConvertExpression(current);
        }

        // 验证表达式是否以参数表达式结束
        if (current is not ParameterExpression)
        {
            throw new InvalidOperationException("Expression must be a property access expression");
        }

        if (parts.Count == 0)
        {
            throw new InvalidOperationException("Expression must access at least one property");
        }

        return string.Join("__", parts);
    }

    /// <summary>
    /// 解包类型转换表达式
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>解包后的表达式</returns>
    private Expression UnwrapConvertExpression(Expression? expression)
    {
        while (expression is UnaryExpression unaryExpression)
        {
            if (unaryExpression.NodeType == ExpressionType.Convert ||
                unaryExpression.NodeType == ExpressionType.ConvertChecked ||
                unaryExpression.NodeType == ExpressionType.TypeAs)
            {
                expression = unaryExpression.Operand;
            }
            else
            {
                break;
            }
        }

        return expression ?? throw new InvalidOperationException("Expression cannot be null");
    }

    /// <summary>
    /// 验证表达式类型
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>是否为支持的表达式类型</returns>
    public bool IsSupportedExpression(Expression expression)
    {
        return expression switch
        {
            MemberExpression => true,
            UnaryExpression unary when unary.NodeType == ExpressionType.Convert ||
                                     unary.NodeType == ExpressionType.ConvertChecked ||
                                     unary.NodeType == ExpressionType.TypeAs => 
                IsSupportedExpression(unary.Operand),
            ParameterExpression => true,
            _ => false
        };
    }

    /// <summary>
    /// 获取表达式的返回类型
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>返回类型</returns>
    public Type GetExpressionType(Expression expression)
    {
        var unwrapped = UnwrapConvertExpression(expression);
        
        if (unwrapped is MemberExpression memberExpression)
        {
            return memberExpression.Type;
        }

        return expression.Type;
    }

    /// <summary>
    /// 分析表达式复杂度
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <returns>表达式复杂度信息</returns>
    public ExpressionComplexity AnalyzeComplexity<TEntity>(Expression<Func<TEntity, object>> expression)
    {
        var complexity = new ExpressionComplexity();
        AnalyzeExpressionRecursive(expression.Body, complexity);
        return complexity;
    }

    /// <summary>
    /// 递归分析表达式
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <param name="complexity">复杂度信息</param>
    private void AnalyzeExpressionRecursive(Expression expression, ExpressionComplexity complexity)
    {
        complexity.NodeCount++;

        switch (expression)
        {
            case MemberExpression memberExpression:
                complexity.PropertyAccessCount++;
                if (memberExpression.Expression != null)
                {
                    AnalyzeExpressionRecursive(memberExpression.Expression, complexity);
                }
                break;

            case UnaryExpression unaryExpression:
                complexity.ConversionCount++;
                AnalyzeExpressionRecursive(unaryExpression.Operand, complexity);
                break;

            case ParameterExpression:
                complexity.ParameterCount++;
                break;

            default:
                complexity.OtherNodeCount++;
                break;
        }
    }
}

/// <summary>
/// 表达式复杂度信息
/// </summary>
public class ExpressionComplexity
{
    /// <summary>
    /// 总节点数
    /// </summary>
    public int NodeCount { get; set; }

    /// <summary>
    /// 属性访问次数
    /// </summary>
    public int PropertyAccessCount { get; set; }

    /// <summary>
    /// 类型转换次数
    /// </summary>
    public int ConversionCount { get; set; }

    /// <summary>
    /// 参数数量
    /// </summary>
    public int ParameterCount { get; set; }

    /// <summary>
    /// 其他节点数量
    /// </summary>
    public int OtherNodeCount { get; set; }

    /// <summary>
    /// 是否为简单表达式
    /// </summary>
    public bool IsSimple => PropertyAccessCount <= 3 && ConversionCount <= 2 && OtherNodeCount == 0;

    /// <summary>
    /// 复杂度等级
    /// </summary>
    public ComplexityLevel Level
    {
        get
        {
            if (IsSimple) return ComplexityLevel.Simple;
            if (PropertyAccessCount <= 5 && OtherNodeCount <= 1) return ComplexityLevel.Medium;
            return ComplexityLevel.Complex;
        }
    }
}

/// <summary>
/// 复杂度等级
/// </summary>
public enum ComplexityLevel
{
    /// <summary>
    /// 简单
    /// </summary>
    Simple,

    /// <summary>
    /// 中等
    /// </summary>
    Medium,

    /// <summary>
    /// 复杂
    /// </summary>
    Complex
}
