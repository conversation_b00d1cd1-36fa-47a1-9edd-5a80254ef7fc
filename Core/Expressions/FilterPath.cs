using DynamicFreesql.Core.Query;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 过滤路径数据结构
/// </summary>
public class FilterPath
{
    /// <summary>
    /// 字段路径段
    /// </summary>
    public string[] Segments { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 过滤操作符
    /// </summary>
    public FilterOperator Operator { get; set; } = FilterOperator.Exact;

    /// <summary>
    /// 原始路径字符串
    /// </summary>
    public string OriginalPath { get; set; } = string.Empty;

    /// <summary>
    /// 字段路径（不包含操作符）
    /// </summary>
    public string FieldPath => string.Join("__", Segments);

    /// <summary>
    /// 操作符字符串
    /// </summary>
    public string OperatorString { get; set; } = string.Empty;

    /// <summary>
    /// 是否为有效路径
    /// </summary>
    public bool IsValid => Segments.Length > 0 && !string.IsNullOrEmpty(OperatorString);

    /// <summary>
    /// 路径深度
    /// </summary>
    public int Depth => Segments.Length;

    /// <summary>
    /// 是否为导航路径（包含多个段）
    /// </summary>
    public bool IsNavigationPath => Segments.Length > 1;

    /// <summary>
    /// 获取最后一个字段名
    /// </summary>
    public string LastFieldName => Segments.Length > 0 ? Segments[^1] : string.Empty;

    /// <summary>
    /// 获取导航路径（除最后一个字段外的所有段）
    /// </summary>
    public string[] NavigationSegments => Segments.Length > 1 ? Segments[..^1] : Array.Empty<string>();

    /// <summary>
    /// 获取导航路径字符串
    /// </summary>
    public string NavigationPath => NavigationSegments.Length > 0 ? string.Join("__", NavigationSegments) : string.Empty;

    /// <summary>
    /// 创建过滤路径
    /// </summary>
    /// <param name="segments">路径段</param>
    /// <param name="operator">操作符</param>
    /// <param name="originalPath">原始路径</param>
    /// <param name="operatorString">操作符字符串</param>
    /// <returns>过滤路径</returns>
    public static FilterPath Create(string[] segments, FilterOperator @operator, string originalPath, string operatorString)
    {
        return new FilterPath
        {
            Segments = segments ?? Array.Empty<string>(),
            Operator = @operator,
            OriginalPath = originalPath ?? string.Empty,
            OperatorString = operatorString ?? string.Empty
        };
    }

    /// <summary>
    /// 创建简单过滤路径
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="operator">操作符</param>
    /// <returns>过滤路径</returns>
    public static FilterPath CreateSimple(string fieldPath, FilterOperator @operator)
    {
        var segments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        var operatorString = GetOperatorString(@operator);
        var originalPath = $"{fieldPath}__{operatorString}";

        return Create(segments, @operator, originalPath, operatorString);
    }

    /// <summary>
    /// 获取操作符字符串
    /// </summary>
    /// <param name="operator">操作符</param>
    /// <returns>操作符字符串</returns>
    public static string GetOperatorString(FilterOperator @operator)
    {
        return @operator switch
        {
            FilterOperator.Exact => "exact",
            FilterOperator.IContains => "icontains",
            FilterOperator.Contains => "contains",
            FilterOperator.StartsWith => "startswith",
            FilterOperator.EndsWith => "endswith",
            FilterOperator.Gte => "gte",
            FilterOperator.Lte => "lte",
            FilterOperator.Gt => "gt",
            FilterOperator.Lt => "lt",
            FilterOperator.In => "in",
            FilterOperator.NotIn => "notin",
            FilterOperator.IsNull => "isnull",
            FilterOperator.IsNotNull => "isnotnull",
            FilterOperator.NotEqual => "ne",
            _ => "exact"
        };
    }

    /// <summary>
    /// 从操作符字符串解析操作符
    /// </summary>
    /// <param name="operatorString">操作符字符串</param>
    /// <returns>操作符</returns>
    public static FilterOperator ParseOperator(string operatorString)
    {
        return operatorString?.ToLowerInvariant() switch
        {
            "exact" => FilterOperator.Exact,
            "icontains" => FilterOperator.IContains,
            "contains" => FilterOperator.Contains,
            "startswith" => FilterOperator.StartsWith,
            "endswith" => FilterOperator.EndsWith,
            "gte" => FilterOperator.Gte,
            "lte" => FilterOperator.Lte,
            "gt" => FilterOperator.Gt,
            "lt" => FilterOperator.Lt,
            "in" => FilterOperator.In,
            "notin" => FilterOperator.NotIn,
            "isnull" => FilterOperator.IsNull,
            "isnotnull" => FilterOperator.IsNotNull,
            "ne" => FilterOperator.NotEqual,
            _ => FilterOperator.Exact
        };
    }

    /// <summary>
    /// 验证操作符是否有效
    /// </summary>
    /// <param name="operatorString">操作符字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidOperator(string operatorString)
    {
        var validOperators = new[]
        {
            "exact", "icontains", "contains", "startswith", "endswith",
            "gte", "lte", "gt", "lt", "in", "notin", "isnull", "isnotnull", "ne"
        };

        return validOperators.Contains(operatorString?.ToLowerInvariant());
    }

    /// <summary>
    /// 获取所有支持的操作符
    /// </summary>
    /// <returns>支持的操作符列表</returns>
    public static string[] GetSupportedOperators()
    {
        return new[]
        {
            "exact", "icontains", "contains", "startswith", "endswith",
            "gte", "lte", "gt", "lt", "in", "notin", "isnull", "isnotnull", "ne"
        };
    }

    /// <summary>
    /// 检查操作符是否适用于指定类型
    /// </summary>
    /// <param name="operator">操作符</param>
    /// <param name="fieldType">字段类型</param>
    /// <returns>是否适用</returns>
    public static bool IsOperatorApplicableToType(FilterOperator @operator, Type fieldType)
    {
        var underlyingType = Nullable.GetUnderlyingType(fieldType) ?? fieldType;

        return @operator switch
        {
            FilterOperator.Exact or FilterOperator.NotEqual or FilterOperator.IsNull or FilterOperator.IsNotNull => true,
            FilterOperator.IContains or FilterOperator.Contains or FilterOperator.StartsWith or FilterOperator.EndsWith => 
                underlyingType == typeof(string),
            FilterOperator.Gte or FilterOperator.Lte or FilterOperator.Gt or FilterOperator.Lt => 
                IsComparableType(underlyingType),
            FilterOperator.In or FilterOperator.NotIn => true,
            _ => false
        };
    }

    /// <summary>
    /// 检查类型是否可比较
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>是否可比较</returns>
    private static bool IsComparableType(Type type)
    {
        return type.IsPrimitive || 
               type == typeof(string) || 
               type == typeof(DateTime) || 
               type == typeof(DateTimeOffset) || 
               type == typeof(TimeSpan) || 
               type == typeof(decimal) ||
               typeof(IComparable).IsAssignableFrom(type);
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{FieldPath}__{OperatorString}";
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(FieldPath, Operator);
    }

    /// <summary>
    /// 比较相等性
    /// </summary>
    /// <param name="obj">比较对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        return obj is FilterPath other && 
               FieldPath == other.FieldPath && 
               Operator == other.Operator;
    }
}
