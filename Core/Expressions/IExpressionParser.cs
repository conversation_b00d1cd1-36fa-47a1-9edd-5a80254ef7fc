using System.Linq.Expressions;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 表达式解析器接口，负责将Expression转换为字段路径字符串
/// </summary>
public interface IExpressionParser
{
    /// <summary>
    /// 将表达式转换为字段路径字符串
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="expression">表达式</param>
    /// <returns>字段路径字符串</returns>
    string GetPropertyPath<TEntity>(Expression<Func<TEntity, object>> expression);
    
    /// <summary>
    /// 验证表达式是否为有效的属性访问表达式
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="expression">表达式</param>
    /// <returns>是否有效</returns>
    bool IsValidPropertyExpression<TEntity>(Expression<Func<TEntity, object>> expression);
}
