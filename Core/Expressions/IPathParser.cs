namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 路径解析器接口，负责解析请求的字段路径字符串
/// </summary>
public interface IPathParser
{
    /// <summary>
    /// 解析过滤字段路径
    /// </summary>
    /// <param name="path">路径字符串，如 "author__name__icontains"</param>
    /// <returns>解析结果</returns>
    FilterPath ParseFilterPath(string path);
    
    /// <summary>
    /// 解析排序字段路径
    /// </summary>
    /// <param name="path">路径字符串，如 "-published_date"</param>
    /// <returns>解析结果</returns>
    OrderingPath ParseOrderingPath(string path);
    
    /// <summary>
    /// 验证路径格式是否正确
    /// </summary>
    /// <param name="path">路径字符串</param>
    /// <returns>是否有效</returns>
    bool IsValidPath(string path);
}
