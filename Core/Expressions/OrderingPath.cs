namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 排序路径数据结构
/// </summary>
public class OrderingPath
{
    /// <summary>
    /// 字段路径段
    /// </summary>
    public string[] Segments { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 排序方向
    /// </summary>
    public OrderDirection Direction { get; set; } = OrderDirection.Ascending;

    /// <summary>
    /// 原始路径字符串
    /// </summary>
    public string OriginalPath { get; set; } = string.Empty;

    /// <summary>
    /// 字段路径（不包含排序方向）
    /// </summary>
    public string FieldPath => string.Join("__", Segments);

    /// <summary>
    /// 是否为有效路径
    /// </summary>
    public bool IsValid => Segments.Length > 0;

    /// <summary>
    /// 路径深度
    /// </summary>
    public int Depth => Segments.Length;

    /// <summary>
    /// 是否为导航路径（包含多个段）
    /// </summary>
    public bool IsNavigationPath => Segments.Length > 1;

    /// <summary>
    /// 获取最后一个字段名
    /// </summary>
    public string LastFieldName => Segments.Length > 0 ? Segments[^1] : string.Empty;

    /// <summary>
    /// 获取导航路径（除最后一个字段外的所有段）
    /// </summary>
    public string[] NavigationSegments => Segments.Length > 1 ? Segments[..^1] : Array.Empty<string>();

    /// <summary>
    /// 获取导航路径字符串
    /// </summary>
    public string NavigationPath => NavigationSegments.Length > 0 ? string.Join("__", NavigationSegments) : string.Empty;

    /// <summary>
    /// 创建排序路径
    /// </summary>
    /// <param name="segments">路径段</param>
    /// <param name="direction">排序方向</param>
    /// <param name="originalPath">原始路径</param>
    /// <returns>排序路径</returns>
    public static OrderingPath Create(string[] segments, OrderDirection direction, string originalPath)
    {
        return new OrderingPath
        {
            Segments = segments ?? Array.Empty<string>(),
            Direction = direction,
            OriginalPath = originalPath ?? string.Empty
        };
    }

    /// <summary>
    /// 创建简单排序路径
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="direction">排序方向</param>
    /// <returns>排序路径</returns>
    public static OrderingPath CreateSimple(string fieldPath, OrderDirection direction = OrderDirection.Ascending)
    {
        var segments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        var originalPath = direction == OrderDirection.Descending ? $"-{fieldPath}" : fieldPath;

        return Create(segments, direction, originalPath);
    }

    /// <summary>
    /// 从路径字符串解析排序路径
    /// </summary>
    /// <param name="pathString">路径字符串</param>
    /// <returns>排序路径</returns>
    public static OrderingPath Parse(string pathString)
    {
        if (string.IsNullOrWhiteSpace(pathString))
        {
            return new OrderingPath { OriginalPath = pathString ?? string.Empty };
        }

        var direction = OrderDirection.Ascending;
        var fieldPath = pathString;

        // 检查是否以 '-' 开头表示降序
        if (pathString.StartsWith('-'))
        {
            direction = OrderDirection.Descending;
            fieldPath = pathString[1..];
        }
        // 检查是否以 '+' 开头表示升序（可选）
        else if (pathString.StartsWith('+'))
        {
            direction = OrderDirection.Ascending;
            fieldPath = pathString[1..];
        }

        var segments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);

        return new OrderingPath
        {
            Segments = segments,
            Direction = direction,
            OriginalPath = pathString
        };
    }

    /// <summary>
    /// 批量解析排序路径
    /// </summary>
    /// <param name="pathStrings">路径字符串数组</param>
    /// <returns>排序路径数组</returns>
    public static OrderingPath[] ParseMultiple(string[] pathStrings)
    {
        if (pathStrings == null || pathStrings.Length == 0)
        {
            return Array.Empty<OrderingPath>();
        }

        return pathStrings
            .Where(p => !string.IsNullOrWhiteSpace(p))
            .Select(Parse)
            .Where(p => p.IsValid)
            .ToArray();
    }

    /// <summary>
    /// 验证排序路径格式
    /// </summary>
    /// <param name="pathString">路径字符串</param>
    /// <returns>是否有效</returns>
    public static bool IsValidFormat(string pathString)
    {
        if (string.IsNullOrWhiteSpace(pathString))
            return false;

        var fieldPath = pathString;

        // 移除排序方向前缀
        if (pathString.StartsWith('-') || pathString.StartsWith('+'))
        {
            fieldPath = pathString[1..];
        }

        // 检查字段路径是否有效
        if (string.IsNullOrWhiteSpace(fieldPath))
            return false;

        // 检查是否包含无效字符
        var invalidChars = new[] { ' ', '\t', '\n', '\r', '/', '\\', '?', '*', '<', '>', '|', '"', '\'' };
        if (fieldPath.Any(c => invalidChars.Contains(c)))
            return false;

        // 检查段是否有效
        var segments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        return segments.Length > 0 && segments.All(s => !string.IsNullOrWhiteSpace(s));
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var prefix = Direction == OrderDirection.Descending ? "-" : "";
        return $"{prefix}{FieldPath}";
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(FieldPath, Direction);
    }

    /// <summary>
    /// 比较相等性
    /// </summary>
    /// <param name="obj">比较对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        return obj is OrderingPath other && 
               FieldPath == other.FieldPath && 
               Direction == other.Direction;
    }
}

/// <summary>
/// 排序方向
/// </summary>
public enum OrderDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,

    /// <summary>
    /// 降序
    /// </summary>
    Descending
}
