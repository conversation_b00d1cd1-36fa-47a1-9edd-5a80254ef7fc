using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Common;
using DynamicFreesql.Core.Query;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 路径解析器实现类
/// </summary>
public class PathParser : IPathParser
{
    private readonly ILogger<PathParser> _logger;

    public PathParser(ILogger<PathParser> logger)
    {
        _logger = logger;
    }

    /// <inheritdoc />
    public FilterPath ParseFilterPath(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            _logger.LogWarning("Attempted to parse null or empty filter path");
            throw new InvalidFieldPathException("Filter path cannot be null or empty");
        }

        try
        {
            var filterPath = ParseFilterPathInternal(path);
            _logger.LogDebug("Parsed filter path: {OriginalPath} -> Field: {FieldPath}, Operator: {Operator}", 
                path, filterPath.FieldPath, filterPath.Operator);
            return filterPath;
        }
        catch (Exception ex) when (!(ex is DynamicQueryException))
        {
            _logger.LogError(ex, "Failed to parse filter path: {Path}", path);
            throw new InvalidFieldPathException(path);
        }
    }

    /// <inheritdoc />
    public OrderingPath ParseOrderingPath(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            _logger.LogWarning("Attempted to parse null or empty ordering path");
            throw new InvalidFieldPathException("Ordering path cannot be null or empty");
        }

        try
        {
            var orderingPath = OrderingPath.Parse(path);
            if (!orderingPath.IsValid)
            {
                throw new InvalidFieldPathException(path);
            }

            _logger.LogDebug("Parsed ordering path: {OriginalPath} -> Field: {FieldPath}, Direction: {Direction}", 
                path, orderingPath.FieldPath, orderingPath.Direction);
            return orderingPath;
        }
        catch (Exception ex) when (!(ex is DynamicQueryException))
        {
            _logger.LogError(ex, "Failed to parse ordering path: {Path}", path);
            throw new InvalidFieldPathException(path);
        }
    }

    /// <inheritdoc />
    public bool IsValidPath(string path)
    {
        if (string.IsNullOrWhiteSpace(path))
            return false;

        try
        {
            // 尝试解析为过滤路径
            if (TryParseAsFilterPath(path))
                return true;

            // 尝试解析为排序路径
            if (OrderingPath.IsValidFormat(path))
                return true;

            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 内部解析过滤路径方法
    /// </summary>
    /// <param name="path">路径字符串</param>
    /// <returns>过滤路径</returns>
    private FilterPath ParseFilterPathInternal(string path)
    {
        // 查找最后一个双下划线，它应该分隔字段路径和操作符
        var lastDoubleUnderscore = path.LastIndexOf("__", StringComparison.Ordinal);
        
        if (lastDoubleUnderscore == -1)
        {
            // 没有找到操作符，使用默认的 exact 操作符
            var segments = path.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
            return FilterPath.Create(segments, FilterOperator.Exact, path, "exact");
        }

        var fieldPath = path[..lastDoubleUnderscore];
        var operatorString = path[(lastDoubleUnderscore + 2)..];

        // 验证操作符
        if (!FilterPath.IsValidOperator(operatorString))
        {
            throw new InvalidOperatorException(operatorString);
        }

        // 验证字段路径
        if (string.IsNullOrWhiteSpace(fieldPath))
        {
            throw new InvalidFieldPathException(path);
        }

        var fieldSegments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        if (fieldSegments.Length == 0)
        {
            throw new InvalidFieldPathException(path);
        }

        var @operator = FilterPath.ParseOperator(operatorString);
        return FilterPath.Create(fieldSegments, @operator, path, operatorString);
    }

    /// <summary>
    /// 尝试解析为过滤路径
    /// </summary>
    /// <param name="path">路径字符串</param>
    /// <returns>是否成功解析</returns>
    private bool TryParseAsFilterPath(string path)
    {
        try
        {
            var filterPath = ParseFilterPathInternal(path);
            return filterPath.IsValid;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 批量解析过滤路径
    /// </summary>
    /// <param name="paths">路径字符串数组</param>
    /// <returns>过滤路径数组</returns>
    public FilterPath[] ParseFilterPaths(string[] paths)
    {
        if (paths == null || paths.Length == 0)
        {
            return Array.Empty<FilterPath>();
        }

        var results = new List<FilterPath>();
        var errors = new List<string>();

        foreach (var path in paths)
        {
            if (string.IsNullOrWhiteSpace(path))
                continue;

            try
            {
                var filterPath = ParseFilterPath(path);
                results.Add(filterPath);
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to parse path '{path}': {ex.Message}");
                _logger.LogWarning(ex, "Failed to parse filter path: {Path}", path);
            }
        }

        if (errors.Count > 0)
        {
            _logger.LogWarning("Failed to parse {ErrorCount} out of {TotalCount} filter paths", 
                errors.Count, paths.Length);
        }

        return results.ToArray();
    }

    /// <summary>
    /// 批量解析排序路径
    /// </summary>
    /// <param name="paths">路径字符串数组</param>
    /// <returns>排序路径数组</returns>
    public OrderingPath[] ParseOrderingPaths(string[] paths)
    {
        if (paths == null || paths.Length == 0)
        {
            return Array.Empty<OrderingPath>();
        }

        var results = new List<OrderingPath>();
        var errors = new List<string>();

        foreach (var path in paths)
        {
            if (string.IsNullOrWhiteSpace(path))
                continue;

            try
            {
                var orderingPath = ParseOrderingPath(path);
                results.Add(orderingPath);
            }
            catch (Exception ex)
            {
                errors.Add($"Failed to parse path '{path}': {ex.Message}");
                _logger.LogWarning(ex, "Failed to parse ordering path: {Path}", path);
            }
        }

        if (errors.Count > 0)
        {
            _logger.LogWarning("Failed to parse {ErrorCount} out of {TotalCount} ordering paths", 
                errors.Count, paths.Length);
        }

        return results.ToArray();
    }

    /// <summary>
    /// 验证路径格式
    /// </summary>
    /// <param name="path">路径字符串</param>
    /// <returns>验证结果</returns>
    public PathValidationResult ValidatePath(string path)
    {
        var result = new PathValidationResult { Path = path };

        if (string.IsNullOrWhiteSpace(path))
        {
            result.IsValid = false;
            result.Errors.Add("Path cannot be null or empty");
            return result;
        }

        // 检查基本格式
        if (!IsBasicFormatValid(path))
        {
            result.IsValid = false;
            result.Errors.Add("Path contains invalid characters or format");
            return result;
        }

        // 尝试解析为过滤路径
        try
        {
            var filterPath = ParseFilterPathInternal(path);
            result.IsValid = true;
            result.PathType = PathType.Filter;
            result.ParsedPath = filterPath;
            return result;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Filter path parsing failed: {ex.Message}");
        }

        // 尝试解析为排序路径
        try
        {
            var orderingPath = OrderingPath.Parse(path);
            if (orderingPath.IsValid)
            {
                result.IsValid = true;
                result.PathType = PathType.Ordering;
                result.ParsedPath = orderingPath;
                return result;
            }
            else
            {
                result.Errors.Add("Ordering path is not valid");
            }
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Ordering path parsing failed: {ex.Message}");
        }

        result.IsValid = false;
        return result;
    }

    /// <summary>
    /// 检查基本格式是否有效
    /// </summary>
    /// <param name="path">路径字符串</param>
    /// <returns>是否有效</returns>
    private static bool IsBasicFormatValid(string path)
    {
        // 检查是否包含无效字符
        var invalidChars = new[] { ' ', '\t', '\n', '\r', '/', '\\', '?', '*', '<', '>', '|', '"', '\'' };
        if (path.Any(c => invalidChars.Contains(c)))
            return false;

        // 检查是否以双下划线开头或结尾
        if (path.StartsWith("__") || path.EndsWith("__"))
            return false;

        // 检查是否包含连续的三个或更多下划线
        if (path.Contains("___"))
            return false;

        return true;
    }
}

/// <summary>
/// 路径验证结果
/// </summary>
public class PathValidationResult
{
    /// <summary>
    /// 原始路径
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 路径类型
    /// </summary>
    public PathType PathType { get; set; }

    /// <summary>
    /// 解析后的路径对象
    /// </summary>
    public object? ParsedPath { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// 路径类型
/// </summary>
public enum PathType
{
    /// <summary>
    /// 未知
    /// </summary>
    Unknown,

    /// <summary>
    /// 过滤路径
    /// </summary>
    Filter,

    /// <summary>
    /// 排序路径
    /// </summary>
    Ordering
}
