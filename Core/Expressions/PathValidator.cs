using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Common;
using DynamicFreesql.Core.Query;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Expressions;

/// <summary>
/// 路径验证器
/// </summary>
public class PathValidator
{
    private readonly IModelMetadata _modelMetadata;
    private readonly ILogger<PathValidator> _logger;
    private readonly DynamicFreesqlOptions _options;

    public PathValidator(
        IModelMetadata modelMetadata,
        ILogger<PathValidator> logger,
        DynamicFreesqlOptions options)
    {
        _modelMetadata = modelMetadata;
        _logger = logger;
        _options = options;
    }

    /// <summary>
    /// 验证过滤路径
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="filterPath">过滤路径</param>
    /// <param name="allowedOperators">允许的操作符列表</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateFilterPath<TEntity>(FilterPath filterPath, FilterOperator[]? allowedOperators = null) 
        where TEntity : class
    {
        return ValidateFilterPath(typeof(TEntity), filterPath, allowedOperators);
    }

    /// <summary>
    /// 验证过滤路径
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="filterPath">过滤路径</param>
    /// <param name="allowedOperators">允许的操作符列表</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateFilterPath(Type entityType, FilterPath filterPath, FilterOperator[]? allowedOperators = null)
    {
        var result = new ValidationResult();

        try
        {
            // 基本验证
            if (filterPath == null)
            {
                result.AddError("Filter path cannot be null");
                return result;
            }

            if (!filterPath.IsValid)
            {
                result.AddError("Filter path is not valid");
                return result;
            }

            // 验证Join深度
            if (filterPath.Depth > _options.MaxJoinDepth)
            {
                result.AddError($"Join depth {filterPath.Depth} exceeds maximum allowed depth {_options.MaxJoinDepth}");
                return result;
            }

            // 验证字段路径存在性
            if (!_modelMetadata.IsValidFieldPath(entityType, filterPath.FieldPath))
            {
                result.AddError($"Field path '{filterPath.FieldPath}' does not exist in entity {entityType.Name}");
                return result;
            }

            // 验证操作符是否在允许列表中
            if (allowedOperators != null && allowedOperators.Length > 0)
            {
                if (!allowedOperators.Contains(filterPath.Operator))
                {
                    result.AddError($"Operator '{filterPath.Operator}' is not allowed for this field");
                    return result;
                }
            }

            // 验证操作符是否适用于字段类型
            var fieldType = _modelMetadata.GetFieldType(entityType, filterPath.FieldPath);
            if (fieldType != null && !FilterPath.IsOperatorApplicableToType(filterPath.Operator, fieldType))
            {
                result.AddError($"Operator '{filterPath.Operator}' is not applicable to field type '{fieldType.Name}'");
                return result;
            }

            result.IsValid = true;
            _logger.LogDebug("Filter path validation passed: {FieldPath} with operator {Operator}", 
                filterPath.FieldPath, filterPath.Operator);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating filter path: {FieldPath}", filterPath.FieldPath);
            result.AddError($"Validation error: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 验证排序路径
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="orderingPath">排序路径</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateOrderingPath<TEntity>(OrderingPath orderingPath) where TEntity : class
    {
        return ValidateOrderingPath(typeof(TEntity), orderingPath);
    }

    /// <summary>
    /// 验证排序路径
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="orderingPath">排序路径</param>
    /// <returns>验证结果</returns>
    public ValidationResult ValidateOrderingPath(Type entityType, OrderingPath orderingPath)
    {
        var result = new ValidationResult();

        try
        {
            // 基本验证
            if (orderingPath == null)
            {
                result.AddError("Ordering path cannot be null");
                return result;
            }

            if (!orderingPath.IsValid)
            {
                result.AddError("Ordering path is not valid");
                return result;
            }

            // 验证Join深度
            if (orderingPath.Depth > _options.MaxJoinDepth)
            {
                result.AddError($"Join depth {orderingPath.Depth} exceeds maximum allowed depth {_options.MaxJoinDepth}");
                return result;
            }

            // 验证字段路径存在性
            if (!_modelMetadata.IsValidFieldPath(entityType, orderingPath.FieldPath))
            {
                result.AddError($"Field path '{orderingPath.FieldPath}' does not exist in entity {entityType.Name}");
                return result;
            }

            // 验证字段是否可排序
            var fieldType = _modelMetadata.GetFieldType(entityType, orderingPath.FieldPath);
            if (fieldType != null && !IsOrderableType(fieldType))
            {
                result.AddError($"Field type '{fieldType.Name}' is not orderable");
                return result;
            }

            result.IsValid = true;
            _logger.LogDebug("Ordering path validation passed: {FieldPath} with direction {Direction}", 
                orderingPath.FieldPath, orderingPath.Direction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating ordering path: {FieldPath}", orderingPath.FieldPath);
            result.AddError($"Validation error: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 批量验证过滤路径
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="filterPaths">过滤路径数组</param>
    /// <param name="allowedOperators">允许的操作符映射</param>
    /// <returns>验证结果数组</returns>
    public ValidationResult[] ValidateFilterPaths<TEntity>(
        FilterPath[] filterPaths, 
        Dictionary<string, FilterOperator[]>? allowedOperators = null) where TEntity : class
    {
        if (filterPaths == null || filterPaths.Length == 0)
        {
            return Array.Empty<ValidationResult>();
        }

        var results = new ValidationResult[filterPaths.Length];

        for (int i = 0; i < filterPaths.Length; i++)
        {
            var filterPath = filterPaths[i];
            var allowedOps = allowedOperators?.TryGetValue(filterPath.FieldPath, out var ops) == true ? ops : null;
            results[i] = ValidateFilterPath<TEntity>(filterPath, allowedOps);
        }

        return results;
    }

    /// <summary>
    /// 批量验证排序路径
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="orderingPaths">排序路径数组</param>
    /// <returns>验证结果数组</returns>
    public ValidationResult[] ValidateOrderingPaths<TEntity>(OrderingPath[] orderingPaths) where TEntity : class
    {
        if (orderingPaths == null || orderingPaths.Length == 0)
        {
            return Array.Empty<ValidationResult>();
        }

        var results = new ValidationResult[orderingPaths.Length];

        for (int i = 0; i < orderingPaths.Length; i++)
        {
            results[i] = ValidateOrderingPath<TEntity>(orderingPaths[i]);
        }

        return results;
    }

    /// <summary>
    /// 验证字段是否在白名单中
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="whitelist">白名单</param>
    /// <returns>是否在白名单中</returns>
    public bool IsFieldInWhitelist(string fieldPath, string[] whitelist)
    {
        if (whitelist == null || whitelist.Length == 0)
            return true; // 没有白名单限制

        return whitelist.Contains(fieldPath, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 验证操作符是否在白名单中
    /// </summary>
    /// <param name="operator">操作符</param>
    /// <param name="whitelist">白名单</param>
    /// <returns>是否在白名单中</returns>
    public bool IsOperatorInWhitelist(FilterOperator @operator, FilterOperator[] whitelist)
    {
        if (whitelist == null || whitelist.Length == 0)
            return true; // 没有白名单限制

        return whitelist.Contains(@operator);
    }

    /// <summary>
    /// 检查类型是否可排序
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>是否可排序</returns>
    private static bool IsOrderableType(Type type)
    {
        var underlyingType = Nullable.GetUnderlyingType(type) ?? type;

        return underlyingType.IsPrimitive ||
               underlyingType == typeof(string) ||
               underlyingType == typeof(DateTime) ||
               underlyingType == typeof(DateTimeOffset) ||
               underlyingType == typeof(TimeSpan) ||
               underlyingType == typeof(decimal) ||
               underlyingType == typeof(Guid) ||
               typeof(IComparable).IsAssignableFrom(underlyingType);
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; private set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; private set; } = new();

    /// <summary>
    /// 添加错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    /// <summary>
    /// 添加警告信息
    /// </summary>
    /// <param name="warning">警告信息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    /// <summary>
    /// 获取所有错误信息的字符串表示
    /// </summary>
    /// <returns>错误信息字符串</returns>
    public string GetErrorsString()
    {
        return string.Join("; ", Errors);
    }

    /// <summary>
    /// 获取所有警告信息的字符串表示
    /// </summary>
    /// <returns>警告信息字符串</returns>
    public string GetWarningsString()
    {
        return string.Join("; ", Warnings);
    }
}
