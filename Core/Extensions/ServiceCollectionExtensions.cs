using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Query;
using DynamicFreesql.Core.Security;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace DynamicFreesql.Core.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加DynamicFreesql服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configure">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddDynamicFreesql(
        this IServiceCollection services, 
        Action<DynamicFreesqlOptions>? configure = null)
    {
        // 配置选项
        var options = new DynamicFreesqlOptions();
        configure?.Invoke(options);
        services.TryAddSingleton(options);
        
        // 注册核心服务
        services.TryAddSingleton<MetadataCacheManager>();
        services.TryAddSingleton<IModelMetadata, ModelMetadata>();
        services.TryAddSingleton<ComplexExpressionHandler>();
        services.TryAddSingleton<IExpressionParser, ExpressionParser>();
        services.TryAddSingleton<IPathParser, PathParser>();
        services.TryAddSingleton<PathValidator>();
        services.TryAddScoped<IFilterBackend, FilterBackend>();
        services.TryAddScoped<IOrderingBackend, OrderingBackend>();
        services.TryAddScoped<IJoinPlanner, JoinPlanner>();
        services.TryAddScoped(typeof(IQueryBuilder<>), typeof(QueryBuilder<>));
        
        // 注册安全服务
        services.TryAddSingleton<ISecurityValidator, SecurityValidator>();
        services.TryAddSingleton<IAuditLogger, AuditLogger>();
        
        return services;
    }
}

/// <summary>
/// DynamicFreesql配置选项
/// </summary>
public class DynamicFreesqlOptions
{
    /// <summary>
    /// 最大Join深度，默认为5
    /// </summary>
    public int MaxJoinDepth { get; set; } = 5;
    
    /// <summary>
    /// 是否启用审计日志，默认为true
    /// </summary>
    public bool EnableAuditLog { get; set; } = true;
    
    /// <summary>
    /// 默认页大小，默认为20
    /// </summary>
    public int DefaultPageSize { get; set; } = 20;
    
    /// <summary>
    /// 最大页大小，默认为1000
    /// </summary>
    public int MaxPageSize { get; set; } = 1000;
    
    /// <summary>
    /// 是否启用缓存，默认为true
    /// </summary>
    public bool EnableCache { get; set; } = true;
    
    /// <summary>
    /// 缓存过期时间（分钟），默认为60分钟
    /// </summary>
    public int CacheExpirationMinutes { get; set; } = 60;
}
