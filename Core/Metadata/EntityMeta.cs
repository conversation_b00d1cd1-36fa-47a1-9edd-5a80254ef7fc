using System.Reflection;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 实体元数据
/// </summary>
public class EntityMeta
{
    /// <summary>
    /// 实体类型
    /// </summary>
    public Type EntityType { get; set; } = null!;
    
    /// <summary>
    /// 表名
    /// </summary>
    public string TableName { get; set; } = string.Empty;
    
    /// <summary>
    /// 属性映射（属性名 -> 列名）
    /// </summary>
    public Dictionary<string, PropertyMeta> Properties { get; set; } = new();
    
    /// <summary>
    /// 导航关系（导航名 -> 导航元数据）
    /// </summary>
    public Dictionary<string, NavigationMeta> Navigations { get; set; } = new();
    
    /// <summary>
    /// 主键属性名
    /// </summary>
    public List<string> PrimaryKeys { get; set; } = new();
    
    /// <summary>
    /// 获取属性元数据
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <returns>属性元数据</returns>
    public PropertyMeta? GetProperty(string propertyName)
    {
        return Properties.TryGetValue(propertyName, out var property) ? property : null;
    }
    
    /// <summary>
    /// 获取导航元数据
    /// </summary>
    /// <param name="navigationName">导航名</param>
    /// <returns>导航元数据</returns>
    public NavigationMeta? GetNavigation(string navigationName)
    {
        return Navigations.TryGetValue(navigationName, out var navigation) ? navigation : null;
    }
    
    /// <summary>
    /// 检查是否存在指定属性
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <returns>是否存在</returns>
    public bool HasProperty(string propertyName)
    {
        return Properties.ContainsKey(propertyName);
    }
    
    /// <summary>
    /// 检查是否存在指定导航
    /// </summary>
    /// <param name="navigationName">导航名</param>
    /// <returns>是否存在</returns>
    public bool HasNavigation(string navigationName)
    {
        return Navigations.ContainsKey(navigationName);
    }
}

/// <summary>
/// 属性元数据
/// </summary>
public class PropertyMeta
{
    /// <summary>
    /// 属性名
    /// </summary>
    public string PropertyName { get; set; } = string.Empty;
    
    /// <summary>
    /// 列名
    /// </summary>
    public string ColumnName { get; set; } = string.Empty;
    
    /// <summary>
    /// 属性类型
    /// </summary>
    public Type PropertyType { get; set; } = null!;
    
    /// <summary>
    /// 是否为主键
    /// </summary>
    public bool IsPrimaryKey { get; set; }
    
    /// <summary>
    /// 是否为外键
    /// </summary>
    public bool IsForeignKey { get; set; }
    
    /// <summary>
    /// 是否可空
    /// </summary>
    public bool IsNullable { get; set; }
    
    /// <summary>
    /// 属性信息
    /// </summary>
    public PropertyInfo PropertyInfo { get; set; } = null!;
    
    /// <summary>
    /// 外键引用的实体类型（如果是外键）
    /// </summary>
    public Type? ForeignKeyEntityType { get; set; }
    
    /// <summary>
    /// 外键引用的属性名（如果是外键）
    /// </summary>
    public string? ForeignKeyPropertyName { get; set; }
}
