using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using FreeSql.DataAnnotations;
using ColumnAttribute = FreeSql.DataAnnotations.ColumnAttribute;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 外键关系分析器
/// </summary>
public static class ForeignKeyAnalyzer
{
    /// <summary>
    /// 分析并设置外键关系
    /// </summary>
    /// <param name="entityMeta">实体元数据</param>
    /// <param name="allEntityTypes">所有实体类型</param>
    public static void AnalyzeForeignKeys(EntityMeta entityMeta, IEnumerable<Type> allEntityTypes)
    {
        var entityTypeMap = allEntityTypes.ToDictionary(t => t.Name, t => t);

        foreach (var property in entityMeta.Properties.Values)
        {
            if (property.IsForeignKey)
            {
                AnalyzeForeignKeyProperty(property, entityTypeMap);
            }
        }

        // 分析导航属性的外键关系
        foreach (var navigation in entityMeta.Navigations.Values)
        {
            AnalyzeNavigationForeignKey(navigation, entityMeta, entityTypeMap);
        }
    }

    /// <summary>
    /// 分析外键属性
    /// </summary>
    /// <param name="property">属性元数据</param>
    /// <param name="entityTypeMap">实体类型映射</param>
    private static void AnalyzeForeignKeyProperty(PropertyMeta property, Dictionary<string, Type> entityTypeMap)
    {
        // 尝试根据命名约定推断外键关系
        // 例如：AuthorId -> Author实体
        if (property.PropertyName.EndsWith("Id", StringComparison.OrdinalIgnoreCase))
        {
            var entityName = property.PropertyName[..^2]; // 移除"Id"后缀
            if (entityTypeMap.TryGetValue(entityName, out var relatedType))
            {
                property.ForeignKeyEntityType = relatedType;
                property.ForeignKeyPropertyName = "Id"; // 假设关联实体的主键为Id
            }
        }

        // 检查是否有显式的外键特性
        var foreignKeyAttribute = property.PropertyInfo.GetCustomAttribute<ForeignKeyAttribute>();
        if (foreignKeyAttribute != null)
        {
            // 根据ForeignKeyAttribute设置外键关系
            // 这里需要根据FreeSql的ForeignKeyAttribute实际属性来实现
        }
    }

    /// <summary>
    /// 分析导航属性的外键关系
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="entityMeta">实体元数据</param>
    /// <param name="entityTypeMap">实体类型映射</param>
    private static void AnalyzeNavigationForeignKey(NavigationMeta navigation, EntityMeta entityMeta, Dictionary<string, Type> entityTypeMap)
    {
        var navigateAttribute = navigation.PropertyInfo.GetCustomAttribute<NavigateAttribute>();
        if (navigateAttribute == null)
            return;

        // 根据导航类型设置外键信息
        switch (navigation.NavigationType)
        {
            case NavigationType.ManyToOne:
                AnalyzeManyToOneNavigation(navigation, entityMeta);
                break;
            case NavigationType.OneToMany:
                AnalyzeOneToManyNavigation(navigation, entityMeta);
                break;
            case NavigationType.OneToOne:
                AnalyzeOneToOneNavigation(navigation, entityMeta);
                break;
            case NavigationType.ManyToMany:
                AnalyzeManyToManyNavigation(navigation, entityMeta);
                break;
        }
    }

    /// <summary>
    /// 分析多对一导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="entityMeta">实体元数据</param>
    private static void AnalyzeManyToOneNavigation(NavigationMeta navigation, EntityMeta entityMeta)
    {
        // 多对一：当前实体包含外键
        var foreignKeyName = $"{navigation.NavigationName}Id";
        if (entityMeta.HasProperty(foreignKeyName))
        {
            navigation.ForeignKeyField = foreignKeyName;
            navigation.RelatedField = "Id"; // 假设关联实体的主键为Id
        }
    }

    /// <summary>
    /// 分析一对多导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="entityMeta">实体元数据</param>
    private static void AnalyzeOneToManyNavigation(NavigationMeta navigation, EntityMeta entityMeta)
    {
        // 一对多：关联实体包含外键
        var primaryKey = entityMeta.PrimaryKeys.FirstOrDefault() ?? "Id";
        navigation.RelatedField = $"{entityMeta.EntityType.Name}Id";
        navigation.ForeignKeyField = primaryKey;
    }

    /// <summary>
    /// 分析一对一导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="entityMeta">实体元数据</param>
    private static void AnalyzeOneToOneNavigation(NavigationMeta navigation, EntityMeta entityMeta)
    {
        // 一对一：可能在任一侧包含外键
        var foreignKeyName = $"{navigation.NavigationName}Id";
        if (entityMeta.HasProperty(foreignKeyName))
        {
            navigation.ForeignKeyField = foreignKeyName;
            navigation.RelatedField = "Id";
        }
        else
        {
            // 外键在关联实体侧
            var primaryKey = entityMeta.PrimaryKeys.FirstOrDefault() ?? "Id";
            navigation.RelatedField = $"{entityMeta.EntityType.Name}Id";
            navigation.ForeignKeyField = primaryKey;
        }
    }

    /// <summary>
    /// 分析多对多导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="entityMeta">实体元数据</param>
    private static void AnalyzeManyToManyNavigation(NavigationMeta navigation, EntityMeta entityMeta)
    {
        // 多对多：需要中间表
        var currentEntityName = entityMeta.EntityType.Name;
        var relatedEntityName = navigation.RelatedEntityType.Name;
        
        // 按字母顺序排列表名，确保一致性
        var tableNames = new[] { currentEntityName, relatedEntityName }.OrderBy(n => n).ToArray();
        navigation.MiddleTableName = $"{tableNames[0]}_{tableNames[1]}";
        
        navigation.MiddleLeftField = $"{currentEntityName}Id";
        navigation.MiddleRightField = $"{relatedEntityName}Id";
    }

    /// <summary>
    /// 推断外键属性名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>外键属性名</returns>
    public static string InferForeignKeyName(Type entityType)
    {
        return $"{entityType.Name}Id";
    }

    /// <summary>
    /// 推断主键属性名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>主键属性名</returns>
    public static string InferPrimaryKeyName(Type entityType)
    {
        // 首先检查是否有显式标记的主键
        var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var primaryKeyProperty = properties.FirstOrDefault(p => 
            p.GetCustomAttribute<ColumnAttribute>()?.IsPrimary == true);
        
        if (primaryKeyProperty != null)
            return primaryKeyProperty.Name;

        // 按约定查找主键：Id 或 {EntityName}Id
        var idProperty = properties.FirstOrDefault(p => p.Name == "Id");
        if (idProperty != null)
            return idProperty.Name;

        var entityIdProperty = properties.FirstOrDefault(p => p.Name == $"{entityType.Name}Id");
        if (entityIdProperty != null)
            return entityIdProperty.Name;

        // 默认返回Id
        return "Id";
    }
}
