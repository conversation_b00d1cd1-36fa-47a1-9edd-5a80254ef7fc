using System.Linq.Expressions;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 模型元数据接口，负责扫描和管理实体元数据
/// </summary>
public interface IModelMetadata
{
    /// <summary>
    /// 获取实体元数据
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <returns>实体元数据</returns>
    EntityMeta GetEntityMeta<TEntity>() where TEntity : class;
    
    /// <summary>
    /// 获取实体元数据
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>实体元数据</returns>
    EntityMeta GetEntityMeta(Type entityType);
    
    /// <summary>
    /// 验证字段路径是否有效
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>是否有效</returns>
    bool IsValidFieldPath<TEntity>(string fieldPath) where TEntity : class;
    
    /// <summary>
    /// 获取字段的最终类型
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>字段类型</returns>
    Type? GetFieldType<TEntity>(string fieldPath) where TEntity : class;
}
