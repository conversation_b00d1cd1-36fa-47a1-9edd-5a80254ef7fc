using System.Reflection;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 反向导航分析器
/// </summary>
public static class InverseNavigationAnalyzer
{
    /// <summary>
    /// 分析并设置反向导航关系
    /// </summary>
    /// <param name="allEntityMetas">所有实体元数据</param>
    public static void AnalyzeInverseNavigations(Dictionary<Type, EntityMeta> allEntityMetas)
    {
        foreach (var entityMeta in allEntityMetas.Values)
        {
            foreach (var navigation in entityMeta.Navigations.Values)
            {
                AnalyzeInverseNavigation(navigation, allEntityMetas);
            }
        }
    }

    /// <summary>
    /// 分析单个导航的反向关系
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="allEntityMetas">所有实体元数据</param>
    private static void AnalyzeInverseNavigation(NavigationMeta navigation, Dictionary<Type, EntityMeta> allEntityMetas)
    {
        if (!allEntityMetas.TryGetValue(navigation.RelatedEntityType, out var relatedEntityMeta))
            return;

        // 查找反向导航
        var inverseNavigation = FindInverseNavigation(navigation, relatedEntityMeta);
        if (inverseNavigation != null)
        {
            // 设置反向导航关系
            navigation.InverseNavigationName = inverseNavigation.NavigationName;
            inverseNavigation.InverseNavigationName = navigation.NavigationName;
            
            // 标记为反向导航
            inverseNavigation.IsInverse = true;
            
            // 确保导航类型的一致性
            EnsureNavigationTypeConsistency(navigation, inverseNavigation);
        }
        else
        {
            // 如果没有找到反向导航，尝试推断
            var inferredInverse = InferInverseNavigation(navigation, relatedEntityMeta);
            if (inferredInverse != null)
            {
                navigation.InverseNavigationName = inferredInverse;
            }
        }
    }

    /// <summary>
    /// 查找反向导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="relatedEntityMeta">关联实体元数据</param>
    /// <returns>反向导航元数据</returns>
    private static NavigationMeta? FindInverseNavigation(NavigationMeta navigation, EntityMeta relatedEntityMeta)
    {
        var sourceEntityType = navigation.PropertyInfo.DeclaringType!;
        
        // 查找指向源实体的导航属性
        foreach (var relatedNavigation in relatedEntityMeta.Navigations.Values)
        {
            if (relatedNavigation.RelatedEntityType == sourceEntityType)
            {
                // 检查是否为互补的导航类型
                if (AreComplementaryNavigationTypes(navigation.NavigationType, relatedNavigation.NavigationType))
                {
                    return relatedNavigation;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// 推断反向导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="relatedEntityMeta">关联实体元数据</param>
    /// <returns>推断的反向导航属性名</returns>
    private static string? InferInverseNavigation(NavigationMeta navigation, EntityMeta relatedEntityMeta)
    {
        var sourceEntityType = navigation.PropertyInfo.DeclaringType!;
        var sourceEntityName = sourceEntityType.Name;

        // 根据导航类型推断反向导航名称
        switch (navigation.NavigationType)
        {
            case NavigationType.ManyToOne:
                // 多对一的反向是一对多，通常是复数形式
                return InferCollectionNavigationName(sourceEntityName);

            case NavigationType.OneToMany:
                // 一对多的反向是多对一，通常是单数形式
                return sourceEntityName;

            case NavigationType.OneToOne:
                // 一对一的反向也是一对一
                return sourceEntityName;

            case NavigationType.ManyToMany:
                // 多对多的反向也是多对多，通常是复数形式
                return InferCollectionNavigationName(sourceEntityName);

            default:
                return null;
        }
    }

    /// <summary>
    /// 推断集合导航名称
    /// </summary>
    /// <param name="entityName">实体名称</param>
    /// <returns>集合导航名称</returns>
    private static string InferCollectionNavigationName(string entityName)
    {
        // 简单的复数化规则
        if (entityName.EndsWith("y", StringComparison.OrdinalIgnoreCase))
        {
            return entityName[..^1] + "ies";
        }
        else if (entityName.EndsWith("s", StringComparison.OrdinalIgnoreCase) ||
                 entityName.EndsWith("sh", StringComparison.OrdinalIgnoreCase) ||
                 entityName.EndsWith("ch", StringComparison.OrdinalIgnoreCase) ||
                 entityName.EndsWith("x", StringComparison.OrdinalIgnoreCase) ||
                 entityName.EndsWith("z", StringComparison.OrdinalIgnoreCase))
        {
            return entityName + "es";
        }
        else
        {
            return entityName + "s";
        }
    }

    /// <summary>
    /// 检查导航类型是否互补
    /// </summary>
    /// <param name="type1">导航类型1</param>
    /// <param name="type2">导航类型2</param>
    /// <returns>是否互补</returns>
    private static bool AreComplementaryNavigationTypes(NavigationType type1, NavigationType type2)
    {
        return (type1 == NavigationType.OneToMany && type2 == NavigationType.ManyToOne) ||
               (type1 == NavigationType.ManyToOne && type2 == NavigationType.OneToMany) ||
               (type1 == NavigationType.OneToOne && type2 == NavigationType.OneToOne) ||
               (type1 == NavigationType.ManyToMany && type2 == NavigationType.ManyToMany);
    }

    /// <summary>
    /// 确保导航类型的一致性
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="inverseNavigation">反向导航元数据</param>
    private static void EnsureNavigationTypeConsistency(NavigationMeta navigation, NavigationMeta inverseNavigation)
    {
        // 确保导航类型的一致性
        switch (navigation.NavigationType)
        {
            case NavigationType.OneToMany:
                if (inverseNavigation.NavigationType != NavigationType.ManyToOne)
                {
                    inverseNavigation.NavigationType = NavigationType.ManyToOne;
                }
                break;

            case NavigationType.ManyToOne:
                if (inverseNavigation.NavigationType != NavigationType.OneToMany)
                {
                    inverseNavigation.NavigationType = NavigationType.OneToMany;
                }
                break;

            case NavigationType.OneToOne:
                if (inverseNavigation.NavigationType != NavigationType.OneToOne)
                {
                    inverseNavigation.NavigationType = NavigationType.OneToOne;
                }
                break;

            case NavigationType.ManyToMany:
                if (inverseNavigation.NavigationType != NavigationType.ManyToMany)
                {
                    inverseNavigation.NavigationType = NavigationType.ManyToMany;
                }
                break;
        }
    }

    /// <summary>
    /// 验证反向导航的一致性
    /// </summary>
    /// <param name="allEntityMetas">所有实体元数据</param>
    /// <returns>验证结果</returns>
    public static List<string> ValidateInverseNavigations(Dictionary<Type, EntityMeta> allEntityMetas)
    {
        var errors = new List<string>();

        foreach (var entityMeta in allEntityMetas.Values)
        {
            foreach (var navigation in entityMeta.Navigations.Values)
            {
                if (!string.IsNullOrEmpty(navigation.InverseNavigationName))
                {
                    var error = ValidateInverseNavigation(navigation, allEntityMetas);
                    if (!string.IsNullOrEmpty(error))
                    {
                        errors.Add(error);
                    }
                }
            }
        }

        return errors;
    }

    /// <summary>
    /// 验证单个反向导航
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="allEntityMetas">所有实体元数据</param>
    /// <returns>错误信息，如果没有错误则返回null</returns>
    private static string? ValidateInverseNavigation(NavigationMeta navigation, Dictionary<Type, EntityMeta> allEntityMetas)
    {
        if (!allEntityMetas.TryGetValue(navigation.RelatedEntityType, out var relatedEntityMeta))
        {
            return $"Related entity type {navigation.RelatedEntityType.Name} not found for navigation {navigation.NavigationName}";
        }

        if (!relatedEntityMeta.Navigations.TryGetValue(navigation.InverseNavigationName!, out var inverseNavigation))
        {
            return $"Inverse navigation {navigation.InverseNavigationName} not found in entity {navigation.RelatedEntityType.Name}";
        }

        if (inverseNavigation.InverseNavigationName != navigation.NavigationName)
        {
            return $"Inverse navigation mismatch: {navigation.NavigationName} -> {navigation.InverseNavigationName} -> {inverseNavigation.InverseNavigationName}";
        }

        return null;
    }
}
