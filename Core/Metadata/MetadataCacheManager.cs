using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 元数据缓存管理器
/// </summary>
public class MetadataCacheManager
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<MetadataCacheManager> _logger;
    private readonly DynamicFreesqlOptions _options;
    private readonly object _lockObject = new();

    public MetadataCacheManager(
        IMemoryCache cache,
        ILogger<MetadataCacheManager> logger,
        DynamicFreesqlOptions options)
    {
        _cache = cache;
        _logger = logger;
        _options = options;
    }

    /// <summary>
    /// 获取或创建实体元数据
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="factory">创建工厂方法</param>
    /// <returns>实体元数据</returns>
    public EntityMeta GetOrCreate(Type entityType, Func<Type, EntityMeta> factory)
    {
        if (!_options.EnableCache)
        {
            return factory(entityType);
        }

        var cacheKey = GetCacheKey(entityType);
        
        if (_cache.TryGetValue(cacheKey, out EntityMeta? cachedMeta) && cachedMeta != null)
        {
            _logger.LogDebug("Retrieved entity metadata from cache for {EntityType}", entityType.Name);
            return cachedMeta;
        }

        lock (_lockObject)
        {
            // 双重检查锁定
            if (_cache.TryGetValue(cacheKey, out cachedMeta) && cachedMeta != null)
            {
                return cachedMeta;
            }

            _logger.LogDebug("Creating and caching entity metadata for {EntityType}", entityType.Name);
            var meta = factory(entityType);
            
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheExpirationMinutes),
                SlidingExpiration = TimeSpan.FromMinutes(_options.CacheExpirationMinutes / 2),
                Priority = CacheItemPriority.High
            };

            _cache.Set(cacheKey, meta, cacheOptions);
            return meta;
        }
    }

    /// <summary>
    /// 获取字段类型缓存
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="factory">创建工厂方法</param>
    /// <returns>字段类型</returns>
    public Type? GetOrCreateFieldType(Type entityType, string fieldPath, Func<Type, string, Type?> factory)
    {
        if (!_options.EnableCache)
        {
            return factory(entityType, fieldPath);
        }

        var cacheKey = GetFieldTypeCacheKey(entityType, fieldPath);
        
        if (_cache.TryGetValue(cacheKey, out Type? cachedType))
        {
            return cachedType;
        }

        var fieldType = factory(entityType, fieldPath);
        
        if (fieldType != null)
        {
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheExpirationMinutes),
                SlidingExpiration = TimeSpan.FromMinutes(_options.CacheExpirationMinutes / 2),
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(cacheKey, fieldType, cacheOptions);
        }

        return fieldType;
    }

    /// <summary>
    /// 获取字段路径验证结果缓存
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="factory">创建工厂方法</param>
    /// <returns>是否有效</returns>
    public bool GetOrCreateFieldPathValidation(Type entityType, string fieldPath, Func<Type, string, bool> factory)
    {
        if (!_options.EnableCache)
        {
            return factory(entityType, fieldPath);
        }

        var cacheKey = GetFieldPathValidationCacheKey(entityType, fieldPath);
        
        if (_cache.TryGetValue(cacheKey, out bool cachedResult))
        {
            return cachedResult;
        }

        var isValid = factory(entityType, fieldPath);
        
        var cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheExpirationMinutes),
            SlidingExpiration = TimeSpan.FromMinutes(_options.CacheExpirationMinutes / 2),
            Priority = CacheItemPriority.Normal
        };

        _cache.Set(cacheKey, isValid, cacheOptions);
        return isValid;
    }

    /// <summary>
    /// 清除指定实体类型的缓存
    /// </summary>
    /// <param name="entityType">实体类型</param>
    public void ClearCache(Type entityType)
    {
        var cacheKey = GetCacheKey(entityType);
        _cache.Remove(cacheKey);
        
        _logger.LogDebug("Cleared cache for entity type {EntityType}", entityType.Name);
    }

    /// <summary>
    /// 清除所有缓存
    /// </summary>
    public void ClearAllCache()
    {
        if (_cache is MemoryCache memoryCache)
        {
            // 这是一个hack方法，因为IMemoryCache没有提供清除所有缓存的方法
            var field = typeof(MemoryCache).GetField("_coherentState", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field?.GetValue(memoryCache) is object coherentState)
            {
                var entriesCollection = coherentState.GetType()
                    .GetProperty("EntriesCollection", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (entriesCollection?.GetValue(coherentState) is System.Collections.IDictionary entries)
                {
                    entries.Clear();
                }
            }
        }
        
        _logger.LogInformation("Cleared all metadata cache");
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计信息</returns>
    public CacheStatistics GetCacheStatistics()
    {
        // 由于IMemoryCache没有提供统计信息的API，这里返回基本信息
        return new CacheStatistics
        {
            IsEnabled = _options.EnableCache,
            ExpirationMinutes = _options.CacheExpirationMinutes
        };
    }

    /// <summary>
    /// 预热缓存
    /// </summary>
    /// <param name="entityTypes">要预热的实体类型</param>
    /// <param name="metadataFactory">元数据工厂</param>
    public void WarmupCache(IEnumerable<Type> entityTypes, Func<Type, EntityMeta> metadataFactory)
    {
        if (!_options.EnableCache)
            return;

        _logger.LogInformation("Starting cache warmup for {Count} entity types", entityTypes.Count());

        foreach (var entityType in entityTypes)
        {
            try
            {
                GetOrCreate(entityType, metadataFactory);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to warmup cache for entity type {EntityType}", entityType.Name);
            }
        }

        _logger.LogInformation("Cache warmup completed");
    }

    /// <summary>
    /// 获取实体元数据缓存键
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>缓存键</returns>
    private static string GetCacheKey(Type entityType)
    {
        return $"EntityMeta_{entityType.FullName}";
    }

    /// <summary>
    /// 获取字段类型缓存键
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>缓存键</returns>
    private static string GetFieldTypeCacheKey(Type entityType, string fieldPath)
    {
        return $"FieldType_{entityType.FullName}_{fieldPath}";
    }

    /// <summary>
    /// 获取字段路径验证缓存键
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>缓存键</returns>
    private static string GetFieldPathValidationCacheKey(Type entityType, string fieldPath)
    {
        return $"FieldPathValidation_{entityType.FullName}_{fieldPath}";
    }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    public int ExpirationMinutes { get; set; }

    /// <summary>
    /// 缓存项数量（如果可获取）
    /// </summary>
    public int? ItemCount { get; set; }

    /// <summary>
    /// 缓存命中率（如果可获取）
    /// </summary>
    public double? HitRate { get; set; }
}
