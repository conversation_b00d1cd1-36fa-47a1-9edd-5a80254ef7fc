using System.Collections.Concurrent;
using System.Reflection;
using FreeSql.DataAnnotations;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 模型元数据实现类
/// </summary>
public class ModelMetadata : IModelMetadata
{
    private readonly MetadataCacheManager _cacheManager;
    private readonly ILogger<ModelMetadata> _logger;
    private readonly DynamicFreesqlOptions _options;

    public ModelMetadata(
        MetadataCacheManager cacheManager,
        ILogger<ModelMetadata> logger,
        DynamicFreesqlOptions options)
    {
        _cacheManager = cacheManager;
        _logger = logger;
        _options = options;
    }

    /// <inheritdoc />
    public EntityMeta GetEntityMeta<TEntity>() where TEntity : class
    {
        return GetEntityMeta(typeof(TEntity));
    }

    /// <inheritdoc />
    public EntityMeta GetEntityMeta(Type entityType)
    {
        return _cacheManager.GetOrCreate(entityType, ScanEntityMeta);
    }

    /// <inheritdoc />
    public bool IsValidFieldPath<TEntity>(string fieldPath) where TEntity : class
    {
        return IsValidFieldPath(typeof(TEntity), fieldPath);
    }

    /// <inheritdoc />
    public Type? GetFieldType<TEntity>(string fieldPath) where TEntity : class
    {
        return GetFieldType(typeof(TEntity), fieldPath);
    }

    /// <summary>
    /// 验证字段路径是否有效
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>是否有效</returns>
    public bool IsValidFieldPath(Type entityType, string fieldPath)
    {
        try
        {
            return GetFieldType(entityType, fieldPath) != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取字段的最终类型
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>字段类型</returns>
    public Type? GetFieldType(Type entityType, string fieldPath)
    {
        return _cacheManager.GetOrCreateFieldType(entityType, fieldPath, GetFieldTypeInternal);
    }

    /// <summary>
    /// 内部获取字段类型方法
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>字段类型</returns>
    private Type? GetFieldTypeInternal(Type entityType, string fieldPath)
    {
        var segments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        var currentType = entityType;
        var currentMeta = GetEntityMeta(currentType);

        foreach (var segment in segments)
        {
            // 检查是否为普通属性
            if (currentMeta.HasProperty(segment))
            {
                var property = currentMeta.GetProperty(segment)!;
                return property.PropertyType;
            }

            // 检查是否为导航属性
            if (currentMeta.HasNavigation(segment))
            {
                var navigation = currentMeta.GetNavigation(segment)!;
                currentType = navigation.RelatedEntityType;
                currentMeta = GetEntityMeta(currentType);
                continue;
            }

            // 字段不存在
            return null;
        }

        return currentType;
    }

    /// <summary>
    /// 扫描实体元数据
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>实体元数据</returns>
    private EntityMeta ScanEntityMeta(Type entityType)
    {
        _logger.LogDebug("Scanning entity metadata for {EntityType}", entityType.Name);

        var entityMeta = new EntityMeta
        {
            EntityType = entityType,
            TableName = GetTableName(entityType)
        };

        // 扫描属性
        ScanProperties(entityType, entityMeta);

        // 扫描导航关系
        ScanNavigations(entityType, entityMeta);

        // 分析外键关系（需要所有实体类型信息，这里先跳过，在后续版本中实现）
        // ForeignKeyAnalyzer.AnalyzeForeignKeys(entityMeta, allEntityTypes);

        _logger.LogDebug("Scanned entity metadata for {EntityType}: {PropertyCount} properties, {NavigationCount} navigations",
            entityType.Name, entityMeta.Properties.Count, entityMeta.Navigations.Count);

        return entityMeta;
    }

    /// <summary>
    /// 获取表名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>表名</returns>
    private static string GetTableName(Type entityType)
    {
        var tableAttribute = entityType.GetCustomAttribute<TableAttribute>();
        return tableAttribute?.Name ?? entityType.Name;
    }

    /// <summary>
    /// 扫描属性
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="entityMeta">实体元数据</param>
    private void ScanProperties(Type entityType, EntityMeta entityMeta)
    {
        var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            // 跳过导航属性
            if (IsNavigationProperty(property))
                continue;

            var propertyMeta = new PropertyMeta
            {
                PropertyName = property.Name,
                ColumnName = GetColumnName(property),
                PropertyType = property.PropertyType,
                PropertyInfo = property,
                IsNullable = IsNullableProperty(property),
                IsPrimaryKey = IsPrimaryKeyProperty(property),
                IsForeignKey = IsForeignKeyProperty(property)
            };

            entityMeta.Properties[property.Name] = propertyMeta;

            if (propertyMeta.IsPrimaryKey)
            {
                entityMeta.PrimaryKeys.Add(property.Name);
            }
        }
    }

    /// <summary>
    /// 扫描导航关系
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="entityMeta">实体元数据</param>
    private void ScanNavigations(Type entityType, EntityMeta entityMeta)
    {
        var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (!IsNavigationProperty(property))
                continue;

            var navigationMeta = CreateNavigationMeta(property);
            if (navigationMeta != null)
            {
                entityMeta.Navigations[property.Name] = navigationMeta;
            }
        }
    }

    /// <summary>
    /// 创建导航元数据
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>导航元数据</returns>
    private NavigationMeta? CreateNavigationMeta(PropertyInfo property)
    {
        var navigateAttribute = property.GetCustomAttribute<NavigateAttribute>();
        if (navigateAttribute == null)
            return null;

        var navigationMeta = new NavigationMeta
        {
            NavigationName = property.Name,
            PropertyInfo = property,
            RelatedEntityType = GetRelatedEntityType(property),
            NavigationType = DetermineNavigationType(property),
            JoinType = JoinType.LeftJoin
        };

        // 设置外键信息
        SetForeignKeyInfo(navigationMeta, navigateAttribute);

        return navigationMeta;
    }

    /// <summary>
    /// 获取关联实体类型
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>关联实体类型</returns>
    private Type GetRelatedEntityType(PropertyInfo property)
    {
        var propertyType = property.PropertyType;

        // 如果是集合类型，获取泛型参数
        if (IsCollectionType(propertyType))
        {
            return propertyType.GetGenericArguments()[0];
        }

        return propertyType;
    }

    /// <summary>
    /// 确定导航类型
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>导航类型</returns>
    private NavigationType DetermineNavigationType(PropertyInfo property)
    {
        var propertyType = property.PropertyType;

        if (IsCollectionType(propertyType))
        {
            // 集合类型，可能是一对多或多对多
            return NavigationType.OneToMany; // 默认为一对多，多对多需要进一步分析
        }

        // 单个实体类型，可能是一对一或多对一
        return NavigationType.ManyToOne; // 默认为多对一
    }

    /// <summary>
    /// 设置外键信息
    /// </summary>
    /// <param name="navigationMeta">导航元数据</param>
    /// <param name="navigateAttribute">导航特性</param>
    private void SetForeignKeyInfo(NavigationMeta navigationMeta, NavigateAttribute navigateAttribute)
    {
        // 从NavigateAttribute获取外键信息
        // 这里需要根据FreeSql的NavigateAttribute实际属性来设置
        // 由于NavigateAttribute的具体属性可能因版本而异，这里提供基本框架

        // 示例：如果NavigateAttribute有相关属性
        // navigationMeta.ForeignKeyField = navigateAttribute.Bind;
        // navigationMeta.RelatedField = navigateAttribute.ManyToMany;
    }

    /// <summary>
    /// 获取列名
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>列名</returns>
    private static string GetColumnName(PropertyInfo property)
    {
        var columnAttribute = property.GetCustomAttribute<ColumnAttribute>();
        return columnAttribute?.Name ?? property.Name;
    }

    /// <summary>
    /// 判断是否为导航属性
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>是否为导航属性</returns>
    private static bool IsNavigationProperty(PropertyInfo property)
    {
        return property.GetCustomAttribute<NavigateAttribute>() != null;
    }

    /// <summary>
    /// 判断是否为可空属性
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>是否可空</returns>
    private static bool IsNullableProperty(PropertyInfo property)
    {
        return Nullable.GetUnderlyingType(property.PropertyType) != null ||
               !property.PropertyType.IsValueType;
    }

    /// <summary>
    /// 判断是否为主键属性
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>是否为主键</returns>
    private static bool IsPrimaryKeyProperty(PropertyInfo property)
    {
        return property.GetCustomAttribute<ColumnAttribute>()?.IsPrimary == true;
    }

    /// <summary>
    /// 判断是否为外键属性
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>是否为外键</returns>
    private static bool IsForeignKeyProperty(PropertyInfo property)
    {
        // 简单的外键判断逻辑：属性名以Id结尾且不是主键
        return property.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase) &&
               !IsPrimaryKeyProperty(property);
    }

    /// <summary>
    /// 判断是否为集合类型
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>是否为集合类型</returns>
    private static bool IsCollectionType(Type type)
    {
        return type.IsGenericType &&
               (type.GetGenericTypeDefinition() == typeof(ICollection<>) ||
                type.GetGenericTypeDefinition() == typeof(IList<>) ||
                type.GetGenericTypeDefinition() == typeof(List<>) ||
                type.GetInterfaces().Any(i => i.IsGenericType &&
                    i.GetGenericTypeDefinition() == typeof(ICollection<>)));
    }
}
