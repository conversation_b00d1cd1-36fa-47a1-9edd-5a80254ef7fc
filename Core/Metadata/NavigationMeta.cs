using System.Reflection;

namespace DynamicFreesql.Core.Metadata;

/// <summary>
/// 导航元数据
/// </summary>
public class NavigationMeta
{
    /// <summary>
    /// 导航属性名
    /// </summary>
    public string NavigationName { get; set; } = string.Empty;
    
    /// <summary>
    /// 关联的实体类型
    /// </summary>
    public Type RelatedEntityType { get; set; } = null!;
    
    /// <summary>
    /// 导航类型
    /// </summary>
    public NavigationType NavigationType { get; set; }
    
    /// <summary>
    /// 外键字段名（在当前实体中）
    /// </summary>
    public string? ForeignKeyField { get; set; }
    
    /// <summary>
    /// 关联字段名（在关联实体中）
    /// </summary>
    public string? RelatedField { get; set; }
    
    /// <summary>
    /// Join类型
    /// </summary>
    public JoinType JoinType { get; set; } = JoinType.LeftJoin;
    
    /// <summary>
    /// 导航属性信息
    /// </summary>
    public PropertyInfo PropertyInfo { get; set; } = null!;
    
    /// <summary>
    /// 是否为反向导航
    /// </summary>
    public bool IsInverse { get; set; }
    
    /// <summary>
    /// 反向导航属性名（如果存在）
    /// </summary>
    public string? InverseNavigationName { get; set; }
    
    /// <summary>
    /// 中间表名（多对多关系时使用）
    /// </summary>
    public string? MiddleTableName { get; set; }
    
    /// <summary>
    /// 中间表的左侧外键字段名（多对多关系时使用）
    /// </summary>
    public string? MiddleLeftField { get; set; }
    
    /// <summary>
    /// 中间表的右侧外键字段名（多对多关系时使用）
    /// </summary>
    public string? MiddleRightField { get; set; }
}

/// <summary>
/// 导航类型
/// </summary>
public enum NavigationType
{
    /// <summary>
    /// 一对一关系
    /// </summary>
    OneToOne,
    
    /// <summary>
    /// 一对多关系
    /// </summary>
    OneToMany,
    
    /// <summary>
    /// 多对一关系
    /// </summary>
    ManyToOne,
    
    /// <summary>
    /// 多对多关系
    /// </summary>
    ManyToMany
}

/// <summary>
/// Join类型
/// </summary>
public enum JoinType
{
    /// <summary>
    /// 内连接
    /// </summary>
    InnerJoin,
    
    /// <summary>
    /// 左连接
    /// </summary>
    LeftJoin,
    
    /// <summary>
    /// 右连接
    /// </summary>
    RightJoin,
    
    /// <summary>
    /// 全连接
    /// </summary>
    FullJoin
}
