using System.Globalization;
using System.Linq.Expressions;
using FreeSql;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Common;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 过滤后端实现类
/// </summary>
public class FilterBackend : IFilterBackend
{
    private readonly IModelMetadata _modelMetadata;
    private readonly ILogger<FilterBackend> _logger;

    public FilterBackend(
        IModelMetadata modelMetadata,
        ILogger<FilterBackend> logger)
    {
        _modelMetadata = modelMetadata;
        _logger = logger;
    }

    /// <inheritdoc />
    public ISelect<TEntity> ApplyFilters<TEntity>(ISelect<TEntity> query, Dictionary<string, object> filterParams) where TEntity : class
    {
        if (filterParams == null || filterParams.Count == 0)
        {
            return query;
        }

        var pathParser = new PathParser(_logger);
        var currentQuery = query;

        foreach (var filterParam in filterParams)
        {
            try
            {
                var filterPath = pathParser.ParseFilterPath(filterParam.Key);
                currentQuery = ApplyFilter(currentQuery, filterPath, filterParam.Value);
                
                _logger.LogDebug("Applied filter: {FilterPath} = {Value}", filterParam.Key, filterParam.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply filter: {FilterPath} = {Value}", filterParam.Key, filterParam.Value);
                throw new DynamicQueryException($"Failed to apply filter '{filterParam.Key}'", ex);
            }
        }

        return currentQuery;
    }

    /// <inheritdoc />
    public ISelect<TEntity> ApplyFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object value) where TEntity : class
    {
        if (filterPath == null || !filterPath.IsValid)
        {
            throw new ArgumentException("Invalid filter path", nameof(filterPath));
        }

        // 获取字段类型
        var fieldType = _modelMetadata.GetFieldType<TEntity>(filterPath.FieldPath);
        if (fieldType == null)
        {
            throw new InvalidFieldPathException(filterPath.FieldPath);
        }

        // 验证和转换过滤值
        if (!ValidateFilterValue(filterPath, value, fieldType))
        {
            throw new ArgumentException($"Invalid filter value for field '{filterPath.FieldPath}'");
        }

        var convertedValue = ConvertFilterValue(value, fieldType);

        // 根据操作符应用过滤条件
        return filterPath.Operator switch
        {
            FilterOperator.Exact => ApplyExactFilter(query, filterPath, convertedValue),
            FilterOperator.IContains => ApplyIContainsFilter(query, filterPath, convertedValue),
            FilterOperator.Contains => ApplyContainsFilter(query, filterPath, convertedValue),
            FilterOperator.StartsWith => ApplyStartsWithFilter(query, filterPath, convertedValue),
            FilterOperator.EndsWith => ApplyEndsWithFilter(query, filterPath, convertedValue),
            FilterOperator.Gte => ApplyGteFilter(query, filterPath, convertedValue),
            FilterOperator.Lte => ApplyLteFilter(query, filterPath, convertedValue),
            FilterOperator.Gt => ApplyGtFilter(query, filterPath, convertedValue),
            FilterOperator.Lt => ApplyLtFilter(query, filterPath, convertedValue),
            FilterOperator.In => ApplyInFilter(query, filterPath, convertedValue),
            FilterOperator.NotIn => ApplyNotInFilter(query, filterPath, convertedValue),
            FilterOperator.IsNull => ApplyIsNullFilter(query, filterPath),
            FilterOperator.IsNotNull => ApplyIsNotNullFilter(query, filterPath),
            FilterOperator.NotEqual => ApplyNotEqualFilter(query, filterPath, convertedValue),
            _ => throw new InvalidOperatorException(filterPath.Operator.ToString())
        };
    }

    /// <inheritdoc />
    public ISelect<TEntity> ApplySearch<TEntity>(ISelect<TEntity> query, string? searchTerm, string[] searchFields) where TEntity : class
    {
        if (string.IsNullOrWhiteSpace(searchTerm) || searchFields == null || searchFields.Length == 0)
        {
            return query;
        }

        // 构建搜索条件：在任一搜索字段中包含搜索词
        var searchConditions = searchFields.Select(field => $"{field} LIKE {{0}}").ToArray();
        var searchPattern = $"%{searchTerm}%";
        var whereClause = string.Join(" OR ", searchConditions);

        return query.Where(whereClause, searchPattern);
    }

    /// <inheritdoc />
    public bool ValidateFilterValue(FilterPath filterPath, object value, Type fieldType)
    {
        if (value == null)
        {
            return filterPath.Operator == FilterOperator.IsNull || 
                   filterPath.Operator == FilterOperator.IsNotNull ||
                   Nullable.GetUnderlyingType(fieldType) != null;
        }

        // 检查操作符是否适用于字段类型
        if (!FilterPath.IsOperatorApplicableToType(filterPath.Operator, fieldType))
        {
            return false;
        }

        // 检查值类型是否兼容
        return IsValueTypeCompatible(value, fieldType);
    }

    /// <inheritdoc />
    public object? ConvertFilterValue(object value, Type targetType)
    {
        if (value == null)
        {
            return null;
        }

        var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

        try
        {
            // 如果类型已经匹配，直接返回
            if (underlyingType.IsAssignableFrom(value.GetType()))
            {
                return value;
            }

            // 特殊处理字符串到其他类型的转换
            if (value is string stringValue)
            {
                return ConvertStringToType(stringValue, underlyingType);
            }

            // 使用Convert.ChangeType进行转换
            return Convert.ChangeType(value, underlyingType, CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to convert value {Value} to type {TargetType}", value, targetType.Name);
            throw new ArgumentException($"Cannot convert value '{value}' to type '{targetType.Name}'", ex);
        }
    }

    /// <summary>
    /// 应用精确匹配过滤
    /// </summary>
    private ISelect<TEntity> ApplyExactFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} = {{0}}", value);
    }

    /// <summary>
    /// 应用不区分大小写包含过滤
    /// </summary>
    private ISelect<TEntity> ApplyIContainsFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is string stringValue)
        {
            return query.Where($"LOWER({filterPath.FieldPath}) LIKE {{0}}", $"%{stringValue.ToLower()}%");
        }
        throw new ArgumentException("IContains operator can only be used with string values");
    }

    /// <summary>
    /// 应用区分大小写包含过滤
    /// </summary>
    private ISelect<TEntity> ApplyContainsFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is string stringValue)
        {
            return query.Where($"{filterPath.FieldPath} LIKE {{0}}", $"%{stringValue}%");
        }
        throw new ArgumentException("Contains operator can only be used with string values");
    }

    /// <summary>
    /// 应用开始于过滤
    /// </summary>
    private ISelect<TEntity> ApplyStartsWithFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is string stringValue)
        {
            return query.Where($"{filterPath.FieldPath} LIKE {{0}}", $"{stringValue}%");
        }
        throw new ArgumentException("StartsWith operator can only be used with string values");
    }

    /// <summary>
    /// 应用结束于过滤
    /// </summary>
    private ISelect<TEntity> ApplyEndsWithFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is string stringValue)
        {
            return query.Where($"{filterPath.FieldPath} LIKE {{0}}", $"%{stringValue}");
        }
        throw new ArgumentException("EndsWith operator can only be used with string values");
    }

    /// <summary>
    /// 应用大于等于过滤
    /// </summary>
    private ISelect<TEntity> ApplyGteFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} >= {{0}}", value);
    }

    /// <summary>
    /// 应用小于等于过滤
    /// </summary>
    private ISelect<TEntity> ApplyLteFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} <= {{0}}", value);
    }

    /// <summary>
    /// 应用大于过滤
    /// </summary>
    private ISelect<TEntity> ApplyGtFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} > {{0}}", value);
    }

    /// <summary>
    /// 应用小于过滤
    /// </summary>
    private ISelect<TEntity> ApplyLtFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} < {{0}}", value);
    }

    /// <summary>
    /// 应用包含于过滤
    /// </summary>
    private ISelect<TEntity> ApplyInFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is System.Collections.IEnumerable enumerable and not string)
        {
            var values = enumerable.Cast<object>().ToArray();
            return query.Where($"{filterPath.FieldPath} IN {{0}}", values);
        }
        throw new ArgumentException("In operator requires an enumerable value");
    }

    /// <summary>
    /// 应用不包含于过滤
    /// </summary>
    private ISelect<TEntity> ApplyNotInFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        if (value is System.Collections.IEnumerable enumerable and not string)
        {
            var values = enumerable.Cast<object>().ToArray();
            return query.Where($"{filterPath.FieldPath} NOT IN {{0}}", values);
        }
        throw new ArgumentException("NotIn operator requires an enumerable value");
    }

    /// <summary>
    /// 应用为空过滤
    /// </summary>
    private ISelect<TEntity> ApplyIsNullFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} IS NULL");
    }

    /// <summary>
    /// 应用不为空过滤
    /// </summary>
    private ISelect<TEntity> ApplyIsNotNullFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} IS NOT NULL");
    }

    /// <summary>
    /// 应用不等于过滤
    /// </summary>
    private ISelect<TEntity> ApplyNotEqualFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object? value) where TEntity : class
    {
        return query.Where($"{filterPath.FieldPath} != {{0}}", value);
    }

    /// <summary>
    /// 检查值类型是否兼容
    /// </summary>
    private static bool IsValueTypeCompatible(object value, Type targetType)
    {
        var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;
        
        // 如果类型直接兼容
        if (underlyingType.IsAssignableFrom(value.GetType()))
        {
            return true;
        }

        // 如果是字符串，检查是否可以转换
        if (value is string)
        {
            return CanConvertStringToType(value.ToString()!, underlyingType);
        }

        // 检查是否可以进行数值转换
        return IsNumericConversionPossible(value, underlyingType);
    }

    /// <summary>
    /// 检查字符串是否可以转换为指定类型
    /// </summary>
    private static bool CanConvertStringToType(string value, Type targetType)
    {
        try
        {
            ConvertStringToType(value, targetType);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 将字符串转换为指定类型
    /// </summary>
    private static object ConvertStringToType(string value, Type targetType)
    {
        if (targetType == typeof(string))
            return value;

        if (targetType == typeof(Guid))
            return Guid.Parse(value);

        if (targetType == typeof(DateTime))
            return DateTime.Parse(value, CultureInfo.InvariantCulture);

        if (targetType == typeof(DateTimeOffset))
            return DateTimeOffset.Parse(value, CultureInfo.InvariantCulture);

        if (targetType == typeof(TimeSpan))
            return TimeSpan.Parse(value, CultureInfo.InvariantCulture);

        if (targetType.IsEnum)
            return Enum.Parse(targetType, value, true);

        return Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// 检查是否可以进行数值转换
    /// </summary>
    private static bool IsNumericConversionPossible(object value, Type targetType)
    {
        var valueType = value.GetType();
        
        // 数值类型之间的转换
        var numericTypes = new[]
        {
            typeof(byte), typeof(sbyte), typeof(short), typeof(ushort),
            typeof(int), typeof(uint), typeof(long), typeof(ulong),
            typeof(float), typeof(double), typeof(decimal)
        };

        return numericTypes.Contains(valueType) && numericTypes.Contains(targetType);
    }
}
