namespace DynamicFreesql.Core.Query;

/// <summary>
/// 过滤操作符枚举
/// </summary>
public enum FilterOperator
{
    /// <summary>
    /// 精确匹配
    /// </summary>
    Exact,
    
    /// <summary>
    /// 不区分大小写包含
    /// </summary>
    IContains,
    
    /// <summary>
    /// 区分大小写包含
    /// </summary>
    Contains,
    
    /// <summary>
    /// 以...开始
    /// </summary>
    StartsWith,
    
    /// <summary>
    /// 以...结束
    /// </summary>
    EndsWith,
    
    /// <summary>
    /// 大于等于
    /// </summary>
    Gte,
    
    /// <summary>
    /// 小于等于
    /// </summary>
    Lte,
    
    /// <summary>
    /// 大于
    /// </summary>
    Gt,
    
    /// <summary>
    /// 小于
    /// </summary>
    Lt,
    
    /// <summary>
    /// 在...范围内
    /// </summary>
    In,
    
    /// <summary>
    /// 不在...范围内
    /// </summary>
    NotIn,
    
    /// <summary>
    /// 为空
    /// </summary>
    IsNull,
    
    /// <summary>
    /// 不为空
    /// </summary>
    IsNotNull,
    
    /// <summary>
    /// 不等于
    /// </summary>
    NotEqual
}
