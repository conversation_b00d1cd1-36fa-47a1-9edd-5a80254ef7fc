using FreeSql;
using DynamicFreesql.Core.Expressions;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 过滤后端接口
/// </summary>
public interface IFilterBackend
{
    /// <summary>
    /// 应用过滤条件到查询
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="filterParams">过滤参数</param>
    /// <returns>应用过滤后的查询对象</returns>
    ISelect<TEntity> ApplyFilters<TEntity>(ISelect<TEntity> query, Dictionary<string, object> filterParams) where TEntity : class;

    /// <summary>
    /// 应用单个过滤条件
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="filterPath">过滤路径</param>
    /// <param name="value">过滤值</param>
    /// <returns>应用过滤后的查询对象</returns>
    ISelect<TEntity> ApplyFilter<TEntity>(ISelect<TEntity> query, FilterPath filterPath, object value) where TEntity : class;

    /// <summary>
    /// 应用搜索条件
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="searchFields">搜索字段</param>
    /// <returns>应用搜索后的查询对象</returns>
    ISelect<TEntity> ApplySearch<TEntity>(ISelect<TEntity> query, string? searchTerm, string[] searchFields) where TEntity : class;

    /// <summary>
    /// 验证过滤值的类型兼容性
    /// </summary>
    /// <param name="filterPath">过滤路径</param>
    /// <param name="value">过滤值</param>
    /// <param name="fieldType">字段类型</param>
    /// <returns>是否兼容</returns>
    bool ValidateFilterValue(FilterPath filterPath, object value, Type fieldType);

    /// <summary>
    /// 转换过滤值到目标类型
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="targetType">目标类型</param>
    /// <returns>转换后的值</returns>
    object? ConvertFilterValue(object value, Type targetType);
}
