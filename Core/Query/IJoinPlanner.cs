using DynamicFreesql.Core.Metadata;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// Join规划器接口
/// </summary>
public interface IJoinPlanner
{
    /// <summary>
    /// 规划Join路径
    /// </summary>
    /// <param name="rootEntityMeta">根实体元数据</param>
    /// <param name="pathSegments">路径段</param>
    /// <returns>Join计划列表</returns>
    List<JoinPlan> PlanJoins(EntityMeta rootEntityMeta, string[] pathSegments);

    /// <summary>
    /// 规划单个字段路径的Join
    /// </summary>
    /// <param name="rootEntityMeta">根实体元数据</param>
    /// <param name="fieldPath">字段路径</param>
    /// <returns>Join计划列表</returns>
    List<JoinPlan> PlanJoinsForField(EntityMeta rootEntityMeta, string fieldPath);

    /// <summary>
    /// 合并多个Join计划
    /// </summary>
    /// <param name="joinPlans">Join计划列表</param>
    /// <returns>合并后的Join计划</returns>
    List<JoinPlan> MergeJoinPlans(IEnumerable<List<JoinPlan>> joinPlans);

    /// <summary>
    /// 验证Join计划的有效性
    /// </summary>
    /// <param name="joinPlans">Join计划列表</param>
    /// <returns>验证结果</returns>
    bool ValidateJoinPlans(List<JoinPlan> joinPlans);

    /// <summary>
    /// 获取Join计划的统计信息
    /// </summary>
    /// <param name="joinPlans">Join计划列表</param>
    /// <returns>统计信息</returns>
    JoinPlanStatistics GetStatistics(List<JoinPlan> joinPlans);
}
