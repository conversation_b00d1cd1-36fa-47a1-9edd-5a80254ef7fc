using FreeSql;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 排序后端接口
/// </summary>
public interface IOrderingBackend
{
    /// <summary>
    /// 应用排序条件到查询
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="ordering">排序参数</param>
    /// <returns>应用排序后的查询对象</returns>
    ISelect<TEntity> ApplyOrdering<TEntity>(ISelect<TEntity> query, string[] ordering) where TEntity : class;

    /// <summary>
    /// 验证排序字段的有效性
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="orderingFields">排序字段</param>
    /// <returns>验证结果</returns>
    bool ValidateOrderingFields<TEntity>(string[] orderingFields) where TEntity : class;

    /// <summary>
    /// 获取默认排序
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <returns>默认排序字段</returns>
    string[] GetDefaultOrdering<TEntity>() where TEntity : class;
}
