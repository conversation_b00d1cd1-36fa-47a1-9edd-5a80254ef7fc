using FreeSql;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 查询构建器接口，负责动态构造查询
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public interface IQueryBuilder<TEntity> : IDisposable where TEntity : class
{
    /// <summary>
    /// 应用过滤条件
    /// </summary>
    /// <param name="filters">过滤参数</param>
    /// <returns>查询构建器</returns>
    IQueryBuilder<TEntity> ApplyFilters(Dictionary<string, object> filters);
    
    /// <summary>
    /// 应用搜索条件
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="searchFields">搜索字段</param>
    /// <returns>查询构建器</returns>
    IQueryBuilder<TEntity> ApplySearch(string? searchTerm, string[] searchFields);
    
    /// <summary>
    /// 应用排序条件
    /// </summary>
    /// <param name="ordering">排序参数</param>
    /// <returns>查询构建器</returns>
    IQueryBuilder<TEntity> ApplyOrdering(string[] ordering);
    
    /// <summary>
    /// 应用分页
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>查询构建器</returns>
    IQueryBuilder<TEntity> ApplyPaging(int page, int pageSize);
    
    /// <summary>
    /// 执行查询并返回列表
    /// </summary>
    /// <returns>实体列表</returns>
    Task<List<TEntity>> ToListAsync();
    
    /// <summary>
    /// 执行查询并返回总数
    /// </summary>
    /// <returns>总数</returns>
    Task<long> CountAsync();
    
    /// <summary>
    /// 获取底层的FreeSql查询对象
    /// </summary>
    /// <returns>FreeSql查询对象</returns>
    ISelect<TEntity> GetQuery();
}
