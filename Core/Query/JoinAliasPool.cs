using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// Join别名池管理器
/// </summary>
public class JoinAliasPool
{
    private readonly ConcurrentDictionary<string, int> _aliasCounters = new();
    private readonly ConcurrentDictionary<string, string> _pathToAliasMap = new();
    private readonly ConcurrentDictionary<string, string> _aliasToPathMap = new();
    private readonly ILogger<JoinAliasPool> _logger;
    private readonly object _lockObject = new();

    public JoinAliasPool(ILogger<JoinAliasPool> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取或创建别名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="joinPath">Join路径</param>
    /// <returns>别名</returns>
    public string GetOrCreateAlias(Type entityType, string joinPath)
    {
        if (string.IsNullOrEmpty(joinPath))
        {
            throw new ArgumentException("Join path cannot be null or empty", nameof(joinPath));
        }

        // 检查是否已存在该路径的别名
        if (_pathToAliasMap.TryGetValue(joinPath, out var existingAlias))
        {
            _logger.LogDebug("Reusing existing alias '{Alias}' for path '{Path}'", existingAlias, joinPath);
            return existingAlias;
        }

        lock (_lockObject)
        {
            // 双重检查锁定
            if (_pathToAliasMap.TryGetValue(joinPath, out existingAlias))
            {
                return existingAlias;
            }

            var alias = GenerateUniqueAlias(entityType, joinPath);
            
            _pathToAliasMap[joinPath] = alias;
            _aliasToPathMap[alias] = joinPath;

            _logger.LogDebug("Created new alias '{Alias}' for path '{Path}' and entity type '{EntityType}'", 
                alias, joinPath, entityType.Name);

            return alias;
        }
    }

    /// <summary>
    /// 检查别名是否已存在
    /// </summary>
    /// <param name="alias">别名</param>
    /// <returns>是否存在</returns>
    public bool AliasExists(string alias)
    {
        return _aliasToPathMap.ContainsKey(alias);
    }

    /// <summary>
    /// 根据别名获取路径
    /// </summary>
    /// <param name="alias">别名</param>
    /// <returns>路径</returns>
    public string? GetPathByAlias(string alias)
    {
        return _aliasToPathMap.TryGetValue(alias, out var path) ? path : null;
    }

    /// <summary>
    /// 根据路径获取别名
    /// </summary>
    /// <param name="joinPath">Join路径</param>
    /// <returns>别名</returns>
    public string? GetAliasByPath(string joinPath)
    {
        return _pathToAliasMap.TryGetValue(joinPath, out var alias) ? alias : null;
    }

    /// <summary>
    /// 获取所有别名
    /// </summary>
    /// <returns>别名列表</returns>
    public IReadOnlyCollection<string> GetAllAliases()
    {
        return _aliasToPathMap.Keys.ToList().AsReadOnly();
    }

    /// <summary>
    /// 获取所有路径
    /// </summary>
    /// <returns>路径列表</returns>
    public IReadOnlyCollection<string> GetAllPaths()
    {
        return _pathToAliasMap.Keys.ToList().AsReadOnly();
    }

    /// <summary>
    /// 清除所有别名
    /// </summary>
    public void Clear()
    {
        lock (_lockObject)
        {
            _aliasCounters.Clear();
            _pathToAliasMap.Clear();
            _aliasToPathMap.Clear();
            
            _logger.LogDebug("Cleared all aliases from pool");
        }
    }

    /// <summary>
    /// 移除指定路径的别名
    /// </summary>
    /// <param name="joinPath">Join路径</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveAlias(string joinPath)
    {
        lock (_lockObject)
        {
            if (_pathToAliasMap.TryRemove(joinPath, out var alias))
            {
                _aliasToPathMap.TryRemove(alias, out _);
                
                _logger.LogDebug("Removed alias '{Alias}' for path '{Path}'", alias, joinPath);
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// 获取别名池统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public AliasPoolStatistics GetStatistics()
    {
        return new AliasPoolStatistics
        {
            TotalAliases = _aliasToPathMap.Count,
            TotalPaths = _pathToAliasMap.Count,
            EntityTypeCounters = _aliasCounters.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
            AliasToPathMappings = _aliasToPathMap.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
        };
    }

    /// <summary>
    /// 批量创建别名
    /// </summary>
    /// <param name="requests">别名请求列表</param>
    /// <returns>别名映射</returns>
    public Dictionary<string, string> CreateAliases(IEnumerable<AliasRequest> requests)
    {
        var results = new Dictionary<string, string>();

        lock (_lockObject)
        {
            foreach (var request in requests)
            {
                var alias = GetOrCreateAlias(request.EntityType, request.JoinPath);
                results[request.JoinPath] = alias;
            }
        }

        _logger.LogDebug("Created {Count} aliases in batch", results.Count);
        return results;
    }

    /// <summary>
    /// 验证别名池的一致性
    /// </summary>
    /// <returns>验证结果</returns>
    public AliasPoolValidationResult Validate()
    {
        var result = new AliasPoolValidationResult();

        try
        {
            // 检查映射的一致性
            foreach (var pathToAlias in _pathToAliasMap)
            {
                if (!_aliasToPathMap.TryGetValue(pathToAlias.Value, out var reversePath) || 
                    reversePath != pathToAlias.Key)
                {
                    result.Errors.Add($"Inconsistent mapping for path '{pathToAlias.Key}' and alias '{pathToAlias.Value}'");
                }
            }

            foreach (var aliasToPath in _aliasToPathMap)
            {
                if (!_pathToAliasMap.TryGetValue(aliasToPath.Value, out var reverseAlias) || 
                    reverseAlias != aliasToPath.Key)
                {
                    result.Errors.Add($"Inconsistent mapping for alias '{aliasToPath.Key}' and path '{aliasToPath.Value}'");
                }
            }

            // 检查别名的唯一性
            var aliasGroups = _aliasToPathMap.Keys.GroupBy(a => a).Where(g => g.Count() > 1);
            foreach (var group in aliasGroups)
            {
                result.Errors.Add($"Duplicate alias found: '{group.Key}'");
            }

            result.IsValid = result.Errors.Count == 0;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    /// <summary>
    /// 生成唯一别名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="joinPath">Join路径</param>
    /// <returns>唯一别名</returns>
    private string GenerateUniqueAlias(Type entityType, string joinPath)
    {
        var baseName = GetBaseAliasName(entityType);
        var counter = _aliasCounters.AddOrUpdate(baseName, 1, (key, value) => value + 1);

        var alias = counter == 1 ? baseName : $"{baseName}{counter}";

        // 确保别名唯一
        while (_aliasToPathMap.ContainsKey(alias))
        {
            counter = _aliasCounters.AddOrUpdate(baseName, counter + 1, (key, value) => Math.Max(value, counter + 1));
            alias = $"{baseName}{counter}";
        }

        return alias;
    }

    /// <summary>
    /// 获取基础别名名称
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>基础别名</returns>
    private static string GetBaseAliasName(Type entityType)
    {
        var name = entityType.Name;
        
        // 移除常见的后缀
        if (name.EndsWith("Entity", StringComparison.OrdinalIgnoreCase))
        {
            name = name[..^6];
        }
        else if (name.EndsWith("Model", StringComparison.OrdinalIgnoreCase))
        {
            name = name[..^5];
        }

        // 转换为小写并限制长度
        name = name.ToLowerInvariant();
        if (name.Length > 8)
        {
            name = name[..8];
        }

        return name;
    }
}

/// <summary>
/// 别名请求
/// </summary>
public class AliasRequest
{
    /// <summary>
    /// 实体类型
    /// </summary>
    public Type EntityType { get; set; } = null!;

    /// <summary>
    /// Join路径
    /// </summary>
    public string JoinPath { get; set; } = string.Empty;

    /// <summary>
    /// 创建别名请求
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="joinPath">Join路径</param>
    /// <returns>别名请求</returns>
    public static AliasRequest Create(Type entityType, string joinPath)
    {
        return new AliasRequest
        {
            EntityType = entityType,
            JoinPath = joinPath
        };
    }
}

/// <summary>
/// 别名池统计信息
/// </summary>
public class AliasPoolStatistics
{
    /// <summary>
    /// 总别名数量
    /// </summary>
    public int TotalAliases { get; set; }

    /// <summary>
    /// 总路径数量
    /// </summary>
    public int TotalPaths { get; set; }

    /// <summary>
    /// 实体类型计数器
    /// </summary>
    public Dictionary<string, int> EntityTypeCounters { get; set; } = new();

    /// <summary>
    /// 别名到路径的映射
    /// </summary>
    public Dictionary<string, string> AliasToPathMappings { get; set; } = new();
}

/// <summary>
/// 别名池验证结果
/// </summary>
public class AliasPoolValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
