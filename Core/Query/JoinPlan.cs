using System.Linq.Expressions;
using DynamicFreesql.Core.Metadata;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// Join计划数据结构
/// </summary>
public class JoinPlan
{
    /// <summary>
    /// Join的实体类型
    /// </summary>
    public Type EntityType { get; set; } = null!;

    /// <summary>
    /// Join的表别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// Join类型
    /// </summary>
    public JoinType JoinType { get; set; } = JoinType.LeftJoin;

    /// <summary>
    /// Join条件表达式（Lambda表达式字符串）
    /// </summary>
    public string JoinCondition { get; set; } = string.Empty;

    /// <summary>
    /// 父级Join计划（用于多级Join）
    /// </summary>
    public JoinPlan? ParentJoin { get; set; }

    /// <summary>
    /// 导航属性名
    /// </summary>
    public string NavigationProperty { get; set; } = string.Empty;

    /// <summary>
    /// 导航元数据
    /// </summary>
    public NavigationMeta? NavigationMeta { get; set; }

    /// <summary>
    /// Join深度（从根实体开始计算）
    /// </summary>
    public int Depth { get; set; }

    /// <summary>
    /// 是否已应用到查询
    /// </summary>
    public bool IsApplied { get; set; }

    /// <summary>
    /// Join路径（从根实体到当前实体的完整路径）
    /// </summary>
    public string JoinPath { get; set; } = string.Empty;

    /// <summary>
    /// 外键字段名
    /// </summary>
    public string? ForeignKeyField { get; set; }

    /// <summary>
    /// 关联字段名
    /// </summary>
    public string? RelatedField { get; set; }

    /// <summary>
    /// 中间表信息（多对多关系时使用）
    /// </summary>
    public MiddleTableInfo? MiddleTable { get; set; }

    /// <summary>
    /// 创建Join计划
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="alias">别名</param>
    /// <param name="joinType">Join类型</param>
    /// <param name="navigationProperty">导航属性名</param>
    /// <param name="depth">深度</param>
    /// <returns>Join计划</returns>
    public static JoinPlan Create(
        Type entityType, 
        string alias, 
        JoinType joinType, 
        string navigationProperty, 
        int depth = 1)
    {
        return new JoinPlan
        {
            EntityType = entityType,
            Alias = alias,
            JoinType = joinType,
            NavigationProperty = navigationProperty,
            Depth = depth,
            JoinPath = navigationProperty
        };
    }

    /// <summary>
    /// 创建子Join计划
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="alias">别名</param>
    /// <param name="joinType">Join类型</param>
    /// <param name="navigationProperty">导航属性名</param>
    /// <param name="parentJoin">父级Join</param>
    /// <returns>子Join计划</returns>
    public static JoinPlan CreateChild(
        Type entityType,
        string alias,
        JoinType joinType,
        string navigationProperty,
        JoinPlan parentJoin)
    {
        return new JoinPlan
        {
            EntityType = entityType,
            Alias = alias,
            JoinType = joinType,
            NavigationProperty = navigationProperty,
            ParentJoin = parentJoin,
            Depth = parentJoin.Depth + 1,
            JoinPath = $"{parentJoin.JoinPath}__{navigationProperty}"
        };
    }

    /// <summary>
    /// 获取完整的Join路径
    /// </summary>
    /// <returns>完整路径</returns>
    public string GetFullPath()
    {
        if (ParentJoin == null)
            return NavigationProperty;

        return $"{ParentJoin.GetFullPath()}__{NavigationProperty}";
    }

    /// <summary>
    /// 获取所有父级Join计划
    /// </summary>
    /// <returns>父级Join计划列表</returns>
    public List<JoinPlan> GetParentJoins()
    {
        var parents = new List<JoinPlan>();
        var current = ParentJoin;

        while (current != null)
        {
            parents.Insert(0, current);
            current = current.ParentJoin;
        }

        return parents;
    }

    /// <summary>
    /// 检查是否为根Join
    /// </summary>
    /// <returns>是否为根Join</returns>
    public bool IsRootJoin => ParentJoin == null;

    /// <summary>
    /// 检查是否为多对多Join
    /// </summary>
    /// <returns>是否为多对多Join</returns>
    public bool IsManyToManyJoin => MiddleTable != null;

    /// <summary>
    /// 生成Join条件
    /// </summary>
    /// <param name="leftAlias">左表别名</param>
    /// <param name="rightAlias">右表别名</param>
    /// <returns>Join条件字符串</returns>
    public string GenerateJoinCondition(string leftAlias, string rightAlias)
    {
        if (IsManyToManyJoin && MiddleTable != null)
        {
            // 多对多关系需要两个Join
            return $"{leftAlias}.{ForeignKeyField} = {MiddleTable.TableName}.{MiddleTable.LeftField} AND " +
                   $"{MiddleTable.TableName}.{MiddleTable.RightField} = {rightAlias}.{RelatedField}";
        }

        // 普通关系
        return $"{leftAlias}.{ForeignKeyField} = {rightAlias}.{RelatedField}";
    }

    /// <summary>
    /// 设置Join条件
    /// </summary>
    /// <param name="foreignKeyField">外键字段</param>
    /// <param name="relatedField">关联字段</param>
    public void SetJoinCondition(string foreignKeyField, string relatedField)
    {
        ForeignKeyField = foreignKeyField;
        RelatedField = relatedField;
    }

    /// <summary>
    /// 设置多对多Join信息
    /// </summary>
    /// <param name="middleTable">中间表信息</param>
    public void SetManyToManyInfo(MiddleTableInfo middleTable)
    {
        MiddleTable = middleTable;
    }

    /// <summary>
    /// 克隆Join计划
    /// </summary>
    /// <returns>克隆的Join计划</returns>
    public JoinPlan Clone()
    {
        return new JoinPlan
        {
            EntityType = EntityType,
            Alias = Alias,
            JoinType = JoinType,
            JoinCondition = JoinCondition,
            ParentJoin = ParentJoin, // 注意：这里是浅拷贝
            NavigationProperty = NavigationProperty,
            NavigationMeta = NavigationMeta,
            Depth = Depth,
            IsApplied = IsApplied,
            JoinPath = JoinPath,
            ForeignKeyField = ForeignKeyField,
            RelatedField = RelatedField,
            MiddleTable = MiddleTable?.Clone()
        };
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{JoinType} {EntityType.Name} AS {Alias} ON {JoinCondition}";
    }

    /// <summary>
    /// 获取哈希码
    /// </summary>
    /// <returns>哈希码</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(EntityType, Alias, JoinPath);
    }

    /// <summary>
    /// 比较相等性
    /// </summary>
    /// <param name="obj">比较对象</param>
    /// <returns>是否相等</returns>
    public override bool Equals(object? obj)
    {
        return obj is JoinPlan other &&
               EntityType == other.EntityType &&
               Alias == other.Alias &&
               JoinPath == other.JoinPath;
    }
}

/// <summary>
/// 中间表信息（用于多对多关系）
/// </summary>
public class MiddleTableInfo
{
    /// <summary>
    /// 中间表名
    /// </summary>
    public string TableName { get; set; } = string.Empty;

    /// <summary>
    /// 左侧外键字段名
    /// </summary>
    public string LeftField { get; set; } = string.Empty;

    /// <summary>
    /// 右侧外键字段名
    /// </summary>
    public string RightField { get; set; } = string.Empty;

    /// <summary>
    /// 中间表别名
    /// </summary>
    public string Alias { get; set; } = string.Empty;

    /// <summary>
    /// 创建中间表信息
    /// </summary>
    /// <param name="tableName">表名</param>
    /// <param name="leftField">左侧字段</param>
    /// <param name="rightField">右侧字段</param>
    /// <param name="alias">别名</param>
    /// <returns>中间表信息</returns>
    public static MiddleTableInfo Create(string tableName, string leftField, string rightField, string alias)
    {
        return new MiddleTableInfo
        {
            TableName = tableName,
            LeftField = leftField,
            RightField = rightField,
            Alias = alias
        };
    }

    /// <summary>
    /// 克隆中间表信息
    /// </summary>
    /// <returns>克隆的中间表信息</returns>
    public MiddleTableInfo Clone()
    {
        return new MiddleTableInfo
        {
            TableName = TableName,
            LeftField = LeftField,
            RightField = RightField,
            Alias = Alias
        };
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{TableName} AS {Alias} ({LeftField}, {RightField})";
    }
}
