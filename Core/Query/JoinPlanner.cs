using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Common;
using DynamicFreesql.Core.Extensions;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// Join规划器实现类
/// </summary>
public class JoinPlanner : IJoinPlanner
{
    private readonly IModelMetadata _modelMetadata;
    private readonly ILogger<JoinPlanner> _logger;
    private readonly DynamicFreesqlOptions _options;
    private readonly JoinAliasPool _aliasPool;

    public JoinPlanner(
        IModelMetadata modelMetadata,
        ILogger<JoinPlanner> logger,
        DynamicFreesqlOptions options,
        JoinAliasPool aliasPool)
    {
        _modelMetadata = modelMetadata;
        _logger = logger;
        _options = options;
        _aliasPool = aliasPool;
    }

    /// <inheritdoc />
    public List<JoinPlan> PlanJoins(EntityMeta rootEntityMeta, string[] pathSegments)
    {
        if (pathSegments == null || pathSegments.Length == 0)
        {
            return new List<JoinPlan>();
        }

        // 验证Join深度
        if (pathSegments.Length > _options.MaxJoinDepth)
        {
            throw new JoinDepthExceededException(_options.MaxJoinDepth, pathSegments.Length);
        }

        var joinPlans = new List<JoinPlan>();
        var currentEntityMeta = rootEntityMeta;
        JoinPlan? parentJoin = null;

        _logger.LogDebug("Planning joins for path: {Path}", string.Join("__", pathSegments));

        for (int i = 0; i < pathSegments.Length - 1; i++) // 最后一个段是字段名，不需要Join
        {
            var segment = pathSegments[i];
            
            // 检查是否为导航属性
            var navigation = currentEntityMeta.GetNavigation(segment);
            if (navigation == null)
            {
                throw new InvalidFieldPathException($"Navigation property '{segment}' not found in entity {currentEntityMeta.EntityType.Name}");
            }

            // 创建Join计划
            var joinPlan = CreateJoinPlan(navigation, parentJoin, i + 1);
            joinPlans.Add(joinPlan);

            // 更新当前实体元数据
            currentEntityMeta = _modelMetadata.GetEntityMeta(navigation.RelatedEntityType);
            parentJoin = joinPlan;

            _logger.LogDebug("Created join plan: {JoinPlan}", joinPlan);
        }

        return joinPlans;
    }

    /// <inheritdoc />
    public List<JoinPlan> PlanJoinsForField(EntityMeta rootEntityMeta, string fieldPath)
    {
        var pathSegments = fieldPath.Split(new[] { "__" }, StringSplitOptions.RemoveEmptyEntries);
        return PlanJoins(rootEntityMeta, pathSegments);
    }

    /// <inheritdoc />
    public List<JoinPlan> MergeJoinPlans(IEnumerable<List<JoinPlan>> joinPlans)
    {
        var mergedPlans = new Dictionary<string, JoinPlan>();

        foreach (var planList in joinPlans)
        {
            foreach (var plan in planList)
            {
                var key = plan.JoinPath;
                if (!mergedPlans.ContainsKey(key))
                {
                    mergedPlans[key] = plan;
                }
                else
                {
                    // 如果已存在相同路径的Join，检查是否需要升级Join类型
                    var existingPlan = mergedPlans[key];
                    if (ShouldUpgradeJoinType(existingPlan.JoinType, plan.JoinType))
                    {
                        existingPlan.JoinType = plan.JoinType;
                    }
                }
            }
        }

        var result = mergedPlans.Values.OrderBy(p => p.Depth).ToList();
        
        _logger.LogDebug("Merged {InputCount} join plan lists into {OutputCount} unique joins", 
            joinPlans.Count(), result.Count);

        return result;
    }

    /// <inheritdoc />
    public bool ValidateJoinPlans(List<JoinPlan> joinPlans)
    {
        try
        {
            // 检查Join深度
            var maxDepth = joinPlans.Count > 0 ? joinPlans.Max(p => p.Depth) : 0;
            if (maxDepth > _options.MaxJoinDepth)
            {
                _logger.LogWarning("Join depth {Depth} exceeds maximum {MaxDepth}", maxDepth, _options.MaxJoinDepth);
                return false;
            }

            // 检查Join计划的一致性
            foreach (var plan in joinPlans)
            {
                if (!ValidateJoinPlan(plan))
                {
                    return false;
                }
            }

            // 检查Join顺序
            if (!ValidateJoinOrder(joinPlans))
            {
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating join plans");
            return false;
        }
    }

    /// <inheritdoc />
    public JoinPlanStatistics GetStatistics(List<JoinPlan> joinPlans)
    {
        return new JoinPlanStatistics
        {
            TotalJoins = joinPlans.Count,
            MaxDepth = joinPlans.Count > 0 ? joinPlans.Max(p => p.Depth) : 0,
            JoinTypeDistribution = joinPlans.GroupBy(p => p.JoinType)
                .ToDictionary(g => g.Key, g => g.Count()),
            EntityTypeDistribution = joinPlans.GroupBy(p => p.EntityType.Name)
                .ToDictionary(g => g.Key, g => g.Count()),
            ManyToManyJoins = joinPlans.Count(p => p.IsManyToManyJoin)
        };
    }

    /// <summary>
    /// 创建Join计划
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <param name="parentJoin">父级Join</param>
    /// <param name="depth">深度</param>
    /// <returns>Join计划</returns>
    private JoinPlan CreateJoinPlan(NavigationMeta navigation, JoinPlan? parentJoin, int depth)
    {
        var joinPath = parentJoin == null
            ? navigation.NavigationName
            : $"{parentJoin.JoinPath}__{navigation.NavigationName}";
        var alias = GenerateAlias(navigation.RelatedEntityType, joinPath);
        var joinType = DetermineJoinType(navigation);

        var joinPlan = parentJoin == null
            ? JoinPlan.Create(navigation.RelatedEntityType, alias, joinType, navigation.NavigationName, depth)
            : JoinPlan.CreateChild(navigation.RelatedEntityType, alias, joinType, navigation.NavigationName, parentJoin);

        // 设置导航元数据
        joinPlan.NavigationMeta = navigation;

        // 设置Join条件
        SetJoinCondition(joinPlan, navigation);

        return joinPlan;
    }

    /// <summary>
    /// 设置Join条件
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    /// <param name="navigation">导航元数据</param>
    private void SetJoinCondition(JoinPlan joinPlan, NavigationMeta navigation)
    {
        switch (navigation.NavigationType)
        {
            case NavigationType.ManyToOne:
                joinPlan.SetJoinCondition(
                    navigation.ForeignKeyField ?? $"{navigation.NavigationName}Id",
                    navigation.RelatedField ?? "Id");
                break;

            case NavigationType.OneToMany:
                joinPlan.SetJoinCondition(
                    navigation.ForeignKeyField ?? "Id",
                    navigation.RelatedField ?? $"{navigation.PropertyInfo.DeclaringType!.Name}Id");
                break;

            case NavigationType.OneToOne:
                joinPlan.SetJoinCondition(
                    navigation.ForeignKeyField ?? $"{navigation.NavigationName}Id",
                    navigation.RelatedField ?? "Id");
                break;

            case NavigationType.ManyToMany:
                if (!string.IsNullOrEmpty(navigation.MiddleTableName))
                {
                    var middleTable = MiddleTableInfo.Create(
                        navigation.MiddleTableName,
                        navigation.MiddleLeftField ?? $"{navigation.PropertyInfo.DeclaringType!.Name}Id",
                        navigation.MiddleRightField ?? $"{navigation.RelatedEntityType.Name}Id",
                        GenerateMiddleTableAlias(navigation.MiddleTableName, joinPlan.Depth));

                    joinPlan.SetManyToManyInfo(middleTable);
                }
                break;
        }
    }

    /// <summary>
    /// 确定Join类型
    /// </summary>
    /// <param name="navigation">导航元数据</param>
    /// <returns>Join类型</returns>
    private static JoinType DetermineJoinType(NavigationMeta navigation)
    {
        // 默认使用LeftJoin，可以根据需要调整
        return navigation.JoinType;
    }

    /// <summary>
    /// 生成表别名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="joinPath">Join路径</param>
    /// <returns>别名</returns>
    private string GenerateAlias(Type entityType, string joinPath)
    {
        return _aliasPool.GetOrCreateAlias(entityType, joinPath);
    }

    /// <summary>
    /// 生成中间表别名
    /// </summary>
    /// <param name="tableName">表名</param>
    /// <param name="depth">深度</param>
    /// <returns>别名</returns>
    private static string GenerateMiddleTableAlias(string tableName, int depth)
    {
        var baseName = tableName.ToLowerInvariant();
        return $"{baseName}_m{depth}";
    }

    /// <summary>
    /// 检查是否应该升级Join类型
    /// </summary>
    /// <param name="existing">现有Join类型</param>
    /// <param name="new">新Join类型</param>
    /// <returns>是否应该升级</returns>
    private static bool ShouldUpgradeJoinType(JoinType existing, JoinType @new)
    {
        // InnerJoin优先级最高，LeftJoin次之
        return (@new == JoinType.InnerJoin && existing != JoinType.InnerJoin) ||
               (@new == JoinType.RightJoin && existing == JoinType.LeftJoin);
    }

    /// <summary>
    /// 验证单个Join计划
    /// </summary>
    /// <param name="plan">Join计划</param>
    /// <returns>是否有效</returns>
    private bool ValidateJoinPlan(JoinPlan plan)
    {
        if (plan.EntityType == null)
        {
            _logger.LogWarning("Join plan has null entity type");
            return false;
        }

        if (string.IsNullOrEmpty(plan.Alias))
        {
            _logger.LogWarning("Join plan has empty alias");
            return false;
        }

        if (plan.Depth <= 0)
        {
            _logger.LogWarning("Join plan has invalid depth: {Depth}", plan.Depth);
            return false;
        }

        return true;
    }

    /// <summary>
    /// 验证Join顺序
    /// </summary>
    /// <param name="joinPlans">Join计划列表</param>
    /// <returns>是否有效</returns>
    private bool ValidateJoinOrder(List<JoinPlan> joinPlans)
    {
        var sortedPlans = joinPlans.OrderBy(p => p.Depth).ToList();
        
        for (int i = 0; i < sortedPlans.Count; i++)
        {
            if (sortedPlans[i].Depth != i + 1)
            {
                _logger.LogWarning("Join plans have invalid depth sequence");
                return false;
            }
        }

        return true;
    }
}

/// <summary>
/// Join计划统计信息
/// </summary>
public class JoinPlanStatistics
{
    /// <summary>
    /// 总Join数量
    /// </summary>
    public int TotalJoins { get; set; }

    /// <summary>
    /// 最大深度
    /// </summary>
    public int MaxDepth { get; set; }

    /// <summary>
    /// Join类型分布
    /// </summary>
    public Dictionary<JoinType, int> JoinTypeDistribution { get; set; } = new();

    /// <summary>
    /// 实体类型分布
    /// </summary>
    public Dictionary<string, int> EntityTypeDistribution { get; set; } = new();

    /// <summary>
    /// 多对多Join数量
    /// </summary>
    public int ManyToManyJoins { get; set; }
}
