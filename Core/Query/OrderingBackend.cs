using FreeSql;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Common;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 排序后端实现类
/// </summary>
public class OrderingBackend : IOrderingBackend
{
    private readonly IModelMetadata _modelMetadata;
    private readonly ILogger<OrderingBackend> _logger;

    public OrderingBackend(
        IModelMetadata modelMetadata,
        ILogger<OrderingBackend> logger)
    {
        _modelMetadata = modelMetadata;
        _logger = logger;
    }

    /// <inheritdoc />
    public ISelect<TEntity> ApplyOrdering<TEntity>(ISelect<TEntity> query, string[] ordering) where TEntity : class
    {
        if (ordering == null || ordering.Length == 0)
        {
            return query;
        }

        var currentQuery = query;
        bool isFirstOrder = true;

        foreach (var orderField in ordering)
        {
            try
            {
                var orderingPath = OrderingPath.Parse(orderField);
                if (!orderingPath.IsValid)
                {
                    _logger.LogWarning("Invalid ordering field: {OrderField}", orderField);
                    continue;
                }

                // 验证字段是否存在
                if (!_modelMetadata.IsValidFieldPath<TEntity>(orderingPath.FieldPath))
                {
                    _logger.LogWarning("Ordering field does not exist: {FieldPath}", orderingPath.FieldPath);
                    continue;
                }

                // 应用排序
                currentQuery = ApplySingleOrdering(currentQuery, orderingPath, isFirstOrder);
                isFirstOrder = false;

                _logger.LogDebug("Applied ordering: {FieldPath} {Direction}", 
                    orderingPath.FieldPath, orderingPath.Direction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply ordering field: {OrderField}", orderField);
                throw new DynamicQueryException($"Failed to apply ordering '{orderField}'", ex);
            }
        }

        return currentQuery;
    }

    /// <inheritdoc />
    public bool ValidateOrderingFields<TEntity>(string[] orderingFields) where TEntity : class
    {
        if (orderingFields == null || orderingFields.Length == 0)
        {
            return true;
        }

        try
        {
            foreach (var orderField in orderingFields)
            {
                var orderingPath = OrderingPath.Parse(orderField);
                if (!orderingPath.IsValid)
                {
                    _logger.LogWarning("Invalid ordering field format: {OrderField}", orderField);
                    return false;
                }

                if (!_modelMetadata.IsValidFieldPath<TEntity>(orderingPath.FieldPath))
                {
                    _logger.LogWarning("Ordering field does not exist: {FieldPath}", orderingPath.FieldPath);
                    return false;
                }

                // 检查字段类型是否可排序
                var fieldType = _modelMetadata.GetFieldType<TEntity>(orderingPath.FieldPath);
                if (fieldType != null && !IsOrderableType(fieldType))
                {
                    _logger.LogWarning("Field type is not orderable: {FieldPath} ({FieldType})", 
                        orderingPath.FieldPath, fieldType.Name);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating ordering fields");
            return false;
        }
    }

    /// <inheritdoc />
    public string[] GetDefaultOrdering<TEntity>() where TEntity : class
    {
        try
        {
            var entityMeta = _modelMetadata.GetEntityMeta<TEntity>();
            
            // 优先使用主键排序
            if (entityMeta.PrimaryKeys.Count > 0)
            {
                return entityMeta.PrimaryKeys.ToArray();
            }

            // 如果没有主键，尝试使用Id字段
            if (entityMeta.HasProperty("Id"))
            {
                return new[] { "Id" };
            }

            // 如果都没有，使用第一个可排序的字段
            var orderableProperty = entityMeta.Properties.Values
                .FirstOrDefault(p => IsOrderableType(p.PropertyType));

            if (orderableProperty != null)
            {
                return new[] { orderableProperty.PropertyName };
            }

            // 如果没有可排序字段，返回空数组
            _logger.LogWarning("No orderable fields found for entity type {EntityType}", typeof(TEntity).Name);
            return Array.Empty<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get default ordering for entity type {EntityType}", typeof(TEntity).Name);
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 应用单个排序条件
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="orderingPath">排序路径</param>
    /// <param name="isFirstOrder">是否为第一个排序条件</param>
    /// <returns>应用排序后的查询对象</returns>
    private ISelect<TEntity> ApplySingleOrdering<TEntity>(
        ISelect<TEntity> query, 
        OrderingPath orderingPath, 
        bool isFirstOrder) where TEntity : class
    {
        var fieldPath = orderingPath.FieldPath;
        var isDescending = orderingPath.Direction == OrderDirection.Descending;

        if (isFirstOrder)
        {
            // 第一个排序条件使用OrderBy
            return isDescending 
                ? query.OrderByDescending(fieldPath)
                : query.OrderBy(fieldPath);
        }
        else
        {
            // 后续排序条件使用ThenBy
            return isDescending 
                ? query.OrderByDescending(fieldPath) // FreeSql可能需要调整这里的API
                : query.OrderBy(fieldPath);
        }
    }

    /// <summary>
    /// 检查类型是否可排序
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>是否可排序</returns>
    private static bool IsOrderableType(Type type)
    {
        var underlyingType = Nullable.GetUnderlyingType(type) ?? type;

        return underlyingType.IsPrimitive ||
               underlyingType == typeof(string) ||
               underlyingType == typeof(DateTime) ||
               underlyingType == typeof(DateTimeOffset) ||
               underlyingType == typeof(TimeSpan) ||
               underlyingType == typeof(decimal) ||
               underlyingType == typeof(Guid) ||
               typeof(IComparable).IsAssignableFrom(underlyingType);
    }

    /// <summary>
    /// 构建排序表达式
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="isDescending">是否降序</param>
    /// <returns>排序表达式字符串</returns>
    private static string BuildOrderExpression(string fieldPath, bool isDescending)
    {
        return isDescending ? $"{fieldPath} DESC" : $"{fieldPath} ASC";
    }

    /// <summary>
    /// 解析复合排序字段
    /// </summary>
    /// <param name="orderingFields">排序字段数组</param>
    /// <returns>解析后的排序路径数组</returns>
    public OrderingPath[] ParseOrderingFields(string[] orderingFields)
    {
        if (orderingFields == null || orderingFields.Length == 0)
        {
            return Array.Empty<OrderingPath>();
        }

        var results = new List<OrderingPath>();

        foreach (var field in orderingFields)
        {
            try
            {
                var orderingPath = OrderingPath.Parse(field);
                if (orderingPath.IsValid)
                {
                    results.Add(orderingPath);
                }
                else
                {
                    _logger.LogWarning("Invalid ordering field: {Field}", field);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse ordering field: {Field}", field);
            }
        }

        return results.ToArray();
    }

    /// <summary>
    /// 获取排序字段的统计信息
    /// </summary>
    /// <param name="orderingFields">排序字段</param>
    /// <returns>统计信息</returns>
    public OrderingStatistics GetOrderingStatistics(string[] orderingFields)
    {
        var orderingPaths = ParseOrderingFields(orderingFields);

        return new OrderingStatistics
        {
            TotalFields = orderingPaths.Length,
            AscendingFields = orderingPaths.Count(p => p.Direction == OrderDirection.Ascending),
            DescendingFields = orderingPaths.Count(p => p.Direction == OrderDirection.Descending),
            NavigationFields = orderingPaths.Count(p => p.IsNavigationPath),
            MaxDepth = orderingPaths.Length > 0 ? orderingPaths.Max(p => p.Depth) : 0,
            FieldPaths = orderingPaths.Select(p => p.FieldPath).ToArray()
        };
    }
}

/// <summary>
/// 排序统计信息
/// </summary>
public class OrderingStatistics
{
    /// <summary>
    /// 总字段数
    /// </summary>
    public int TotalFields { get; set; }

    /// <summary>
    /// 升序字段数
    /// </summary>
    public int AscendingFields { get; set; }

    /// <summary>
    /// 降序字段数
    /// </summary>
    public int DescendingFields { get; set; }

    /// <summary>
    /// 导航字段数
    /// </summary>
    public int NavigationFields { get; set; }

    /// <summary>
    /// 最大深度
    /// </summary>
    public int MaxDepth { get; set; }

    /// <summary>
    /// 字段路径列表
    /// </summary>
    public string[] FieldPaths { get; set; } = Array.Empty<string>();
}
