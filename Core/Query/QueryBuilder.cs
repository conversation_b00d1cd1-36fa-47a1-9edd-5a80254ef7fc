using FreeSql;
using Microsoft.Extensions.Logging;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Expressions;
using DynamicFreesql.Core.Common;

namespace DynamicFreesql.Core.Query;

/// <summary>
/// 查询构建器实现类
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class QueryBuilder<TEntity> : IQueryBuilder<TEntity> where TEntity : class
{
    private readonly IFreeSql _freeSql;
    private readonly IModelMetadata _modelMetadata;
    private readonly IJoinPlanner _joinPlanner;
    private readonly IFilterBackend _filterBackend;
    private readonly IOrderingBackend _orderingBackend;
    private readonly ILogger<QueryBuilder<TEntity>> _logger;
    
    private ISelect<TEntity> _query;
    private readonly List<JoinPlan> _appliedJoins = new();
    private bool _isDisposed;

    public QueryBuilder(
        IFreeSql freeSql,
        IModelMetadata modelMetadata,
        IJoinPlanner joinPlanner,
        IFilterBackend filterBackend,
        IOrderingBackend orderingBackend,
        ILogger<QueryBuilder<TEntity>> logger)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _modelMetadata = modelMetadata ?? throw new ArgumentNullException(nameof(modelMetadata));
        _joinPlanner = joinPlanner ?? throw new ArgumentNullException(nameof(joinPlanner));
        _filterBackend = filterBackend ?? throw new ArgumentNullException(nameof(filterBackend));
        _orderingBackend = orderingBackend ?? throw new ArgumentNullException(nameof(orderingBackend));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _query = _freeSql.Select<TEntity>();
        
        _logger.LogDebug("Created QueryBuilder for entity type {EntityType}", typeof(TEntity).Name);
    }

    /// <inheritdoc />
    public IQueryBuilder<TEntity> ApplyFilters(Dictionary<string, object> filters)
    {
        if (filters == null || filters.Count == 0)
        {
            return this;
        }

        try
        {
            // 解析所有过滤路径并规划Join
            var pathParser = new PathParser(_logger);
            var entityMeta = _modelMetadata.GetEntityMeta<TEntity>();
            var allJoinPlans = new List<List<JoinPlan>>();

            foreach (var filter in filters)
            {
                var filterPath = pathParser.ParseFilterPath(filter.Key);
                if (filterPath.IsNavigationPath)
                {
                    var joinPlans = _joinPlanner.PlanJoinsForField(entityMeta, filterPath.FieldPath);
                    if (joinPlans.Count > 0)
                    {
                        allJoinPlans.Add(joinPlans);
                    }
                }
            }

            // 合并并应用Join
            if (allJoinPlans.Count > 0)
            {
                var mergedJoins = _joinPlanner.MergeJoinPlans(allJoinPlans);
                ApplyJoins(mergedJoins);
            }

            // 应用过滤条件
            _query = _filterBackend.ApplyFilters(_query, filters);
            
            _logger.LogDebug("Applied {FilterCount} filters to query", filters.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply filters to query");
            throw new DynamicQueryException("Failed to apply filters", ex);
        }

        return this;
    }

    /// <inheritdoc />
    public IQueryBuilder<TEntity> ApplySearch(string? searchTerm, string[] searchFields)
    {
        if (string.IsNullOrWhiteSpace(searchTerm) || searchFields == null || searchFields.Length == 0)
        {
            return this;
        }

        try
        {
            // 规划搜索字段的Join
            var entityMeta = _modelMetadata.GetEntityMeta<TEntity>();
            var allJoinPlans = new List<List<JoinPlan>>();

            foreach (var searchField in searchFields)
            {
                if (searchField.Contains("__"))
                {
                    var joinPlans = _joinPlanner.PlanJoinsForField(entityMeta, searchField);
                    if (joinPlans.Count > 0)
                    {
                        allJoinPlans.Add(joinPlans);
                    }
                }
            }

            // 合并并应用Join
            if (allJoinPlans.Count > 0)
            {
                var mergedJoins = _joinPlanner.MergeJoinPlans(allJoinPlans);
                ApplyJoins(mergedJoins);
            }

            // 应用搜索条件
            _query = _filterBackend.ApplySearch(_query, searchTerm, searchFields);
            
            _logger.LogDebug("Applied search term '{SearchTerm}' to fields: {SearchFields}", 
                searchTerm, string.Join(", ", searchFields));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply search to query");
            throw new DynamicQueryException("Failed to apply search", ex);
        }

        return this;
    }

    /// <inheritdoc />
    public IQueryBuilder<TEntity> ApplyOrdering(string[] ordering)
    {
        if (ordering == null || ordering.Length == 0)
        {
            return this;
        }

        try
        {
            // 规划排序字段的Join
            var entityMeta = _modelMetadata.GetEntityMeta<TEntity>();
            var allJoinPlans = new List<List<JoinPlan>>();

            foreach (var orderField in ordering)
            {
                var orderingPath = OrderingPath.Parse(orderField);
                if (orderingPath.IsNavigationPath)
                {
                    var joinPlans = _joinPlanner.PlanJoinsForField(entityMeta, orderingPath.FieldPath);
                    if (joinPlans.Count > 0)
                    {
                        allJoinPlans.Add(joinPlans);
                    }
                }
            }

            // 合并并应用Join
            if (allJoinPlans.Count > 0)
            {
                var mergedJoins = _joinPlanner.MergeJoinPlans(allJoinPlans);
                ApplyJoins(mergedJoins);
            }

            // 应用排序条件
            _query = _orderingBackend.ApplyOrdering(_query, ordering);
            
            _logger.LogDebug("Applied ordering: {Ordering}", string.Join(", ", ordering));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply ordering to query");
            throw new DynamicQueryException("Failed to apply ordering", ex);
        }

        return this;
    }

    /// <inheritdoc />
    public IQueryBuilder<TEntity> ApplyPaging(int page, int pageSize)
    {
        if (page < 1)
        {
            throw new ArgumentException("Page number must be greater than 0", nameof(page));
        }

        if (pageSize < 1)
        {
            throw new ArgumentException("Page size must be greater than 0", nameof(pageSize));
        }

        try
        {
            var skip = (page - 1) * pageSize;
            _query = _query.Skip(skip).Take(pageSize);
            
            _logger.LogDebug("Applied paging: page {Page}, size {PageSize} (skip {Skip})", page, pageSize, skip);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply paging to query");
            throw new DynamicQueryException("Failed to apply paging", ex);
        }

        return this;
    }

    /// <inheritdoc />
    public async Task<List<TEntity>> ToListAsync()
    {
        try
        {
            var result = await _query.ToListAsync();
            _logger.LogDebug("Executed query and returned {Count} results", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute query");
            throw new DynamicQueryException("Failed to execute query", ex);
        }
    }

    /// <inheritdoc />
    public async Task<long> CountAsync()
    {
        try
        {
            var count = await _query.CountAsync();
            _logger.LogDebug("Executed count query and returned {Count}", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute count query");
            throw new DynamicQueryException("Failed to execute count query", ex);
        }
    }

    /// <inheritdoc />
    public ISelect<TEntity> GetQuery()
    {
        return _query;
    }

    /// <summary>
    /// 应用Join计划
    /// </summary>
    /// <param name="joinPlans">Join计划列表</param>
    private void ApplyJoins(List<JoinPlan> joinPlans)
    {
        foreach (var joinPlan in joinPlans)
        {
            // 检查是否已经应用过相同的Join
            if (_appliedJoins.Any(j => j.JoinPath == joinPlan.JoinPath))
            {
                continue;
            }

            try
            {
                ApplyJoin(joinPlan);
                _appliedJoins.Add(joinPlan);
                joinPlan.IsApplied = true;
                
                _logger.LogDebug("Applied join: {JoinPlan}", joinPlan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply join: {JoinPlan}", joinPlan);
                throw new DynamicQueryException($"Failed to apply join '{joinPlan.JoinPath}'", ex);
            }
        }
    }

    /// <summary>
    /// 应用单个Join
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    private void ApplyJoin(JoinPlan joinPlan)
    {
        // 根据Join类型应用不同的Join方法
        switch (joinPlan.JoinType)
        {
            case JoinType.LeftJoin:
                ApplyLeftJoin(joinPlan);
                break;
            case JoinType.InnerJoin:
                ApplyInnerJoin(joinPlan);
                break;
            case JoinType.RightJoin:
                ApplyRightJoin(joinPlan);
                break;
            case JoinType.FullJoin:
                ApplyFullJoin(joinPlan);
                break;
            default:
                throw new NotSupportedException($"Join type {joinPlan.JoinType} is not supported");
        }
    }

    /// <summary>
    /// 应用左连接
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    private void ApplyLeftJoin(JoinPlan joinPlan)
    {
        if (joinPlan.IsManyToManyJoin)
        {
            // 多对多关系需要两个Join
            var middleTable = joinPlan.MiddleTable!;
            _query = _query.LeftJoin($"{middleTable.TableName} {middleTable.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {middleTable.Alias}.{middleTable.LeftField}");
            _query = _query.LeftJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"{middleTable.Alias}.{middleTable.RightField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
        else
        {
            _query = _query.LeftJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
    }

    /// <summary>
    /// 应用内连接
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    private void ApplyInnerJoin(JoinPlan joinPlan)
    {
        if (joinPlan.IsManyToManyJoin)
        {
            var middleTable = joinPlan.MiddleTable!;
            _query = _query.InnerJoin($"{middleTable.TableName} {middleTable.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {middleTable.Alias}.{middleTable.LeftField}");
            _query = _query.InnerJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"{middleTable.Alias}.{middleTable.RightField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
        else
        {
            _query = _query.InnerJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
    }

    /// <summary>
    /// 应用右连接
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    private void ApplyRightJoin(JoinPlan joinPlan)
    {
        if (joinPlan.IsManyToManyJoin)
        {
            var middleTable = joinPlan.MiddleTable!;
            _query = _query.RightJoin($"{middleTable.TableName} {middleTable.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {middleTable.Alias}.{middleTable.LeftField}");
            _query = _query.RightJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"{middleTable.Alias}.{middleTable.RightField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
        else
        {
            _query = _query.RightJoin($"{GetTableName(joinPlan.EntityType)} {joinPlan.Alias}", 
                $"a.{joinPlan.ForeignKeyField} = {joinPlan.Alias}.{joinPlan.RelatedField}");
        }
    }

    /// <summary>
    /// 应用全连接
    /// </summary>
    /// <param name="joinPlan">Join计划</param>
    private void ApplyFullJoin(JoinPlan joinPlan)
    {
        // FreeSql可能不直接支持FullJoin，这里使用LeftJoin作为替代
        ApplyLeftJoin(joinPlan);
    }

    /// <summary>
    /// 获取实体类型对应的表名
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>表名</returns>
    private string GetTableName(Type entityType)
    {
        var entityMeta = _modelMetadata.GetEntityMeta(entityType);
        return entityMeta.TableName;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_isDisposed)
        {
            _query?.Dispose();
            _isDisposed = true;
        }
    }
}
