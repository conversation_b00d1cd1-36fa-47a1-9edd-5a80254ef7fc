namespace DynamicFreesql.Core.Security;

/// <summary>
/// 审计日志接口
/// </summary>
public interface IAuditLogger
{
    /// <summary>
    /// 记录查询审计日志
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <param name="operation">操作类型</param>
    /// <param name="parameters">参数</param>
    /// <param name="userId">用户ID</param>
    void LogQuery(Type entityType, string operation, object parameters, string? userId = null);

    /// <summary>
    /// 记录安全事件
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="message">消息</param>
    /// <param name="userId">用户ID</param>
    void LogSecurityEvent(string eventType, string message, string? userId = null);
}

/// <summary>
/// 审计日志实现类
/// </summary>
public class AuditLogger : IAuditLogger
{
    /// <inheritdoc />
    public void LogQuery(Type entityType, string operation, object parameters, string? userId = null)
    {
        // 这里可以实现具体的日志记录逻辑
        // 例如写入数据库、文件或发送到日志服务
    }

    /// <inheritdoc />
    public void LogSecurityEvent(string eventType, string message, string? userId = null)
    {
        // 这里可以实现具体的安全事件记录逻辑
    }
}
