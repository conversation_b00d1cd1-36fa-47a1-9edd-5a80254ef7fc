namespace DynamicFreesql.Core.Security;

/// <summary>
/// 安全验证器接口
/// </summary>
public interface ISecurityValidator
{
    /// <summary>
    /// 验证字段是否在白名单中
    /// </summary>
    /// <param name="fieldPath">字段路径</param>
    /// <param name="whitelist">白名单</param>
    /// <returns>是否通过验证</returns>
    bool ValidateFieldWhitelist(string fieldPath, string[] whitelist);

    /// <summary>
    /// 验证操作符是否在白名单中
    /// </summary>
    /// <param name="operator">操作符</param>
    /// <param name="whitelist">白名单</param>
    /// <returns>是否通过验证</returns>
    bool ValidateOperatorWhitelist(string @operator, string[] whitelist);

    /// <summary>
    /// 验证Join深度
    /// </summary>
    /// <param name="depth">深度</param>
    /// <param name="maxDepth">最大深度</param>
    /// <returns>是否通过验证</returns>
    bool ValidateJoinDepth(int depth, int maxDepth);
}

/// <summary>
/// 安全验证器实现类
/// </summary>
public class SecurityValidator : ISecurityValidator
{
    /// <inheritdoc />
    public bool ValidateFieldWhitelist(string fieldPath, string[] whitelist)
    {
        if (whitelist == null || whitelist.Length == 0)
            return true;

        return whitelist.Contains(fieldPath, StringComparer.OrdinalIgnoreCase);
    }

    /// <inheritdoc />
    public bool ValidateOperatorWhitelist(string @operator, string[] whitelist)
    {
        if (whitelist == null || whitelist.Length == 0)
            return true;

        return whitelist.Contains(@operator, StringComparer.OrdinalIgnoreCase);
    }

    /// <inheritdoc />
    public bool ValidateJoinDepth(int depth, int maxDepth)
    {
        return depth <= maxDepth;
    }
}
