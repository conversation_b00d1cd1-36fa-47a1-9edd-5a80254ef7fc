// 这个文件用于验证类型是否正确，编译通过即表示类型错误已修复
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FreeSql;
using DynamicFreesql.Core.Extensions;
using DynamicFreesql.Core.Controllers;
using DynamicFreesql.Core.Query;
using DynamicFreesql.Core.Metadata;
using DynamicFreesql.Core.Expressions;

using ServiceCollection = Microsoft.Extensions.DependencyInjection.ServiceCollection;

namespace DynamicFreesql.Core;

/// <summary>
/// 类型验证类
/// </summary>
public static class TypeValidation
{
    /// <summary>
    /// 验证所有类型是否正确
    /// </summary>
    public static void ValidateTypes()
    {
        var services = new ServiceCollection();
        
        // 添加基础服务
        services.AddLogging();
        services.AddMemoryCache();
        
        // 添加FreeSql（模拟）
        services.AddSingleton<IFreeSql>(provider => 
        {
            // 这里只是为了编译验证，实际使用时需要正确配置FreeSql
            return new FreeSqlBuilder()
                .UseConnectionString(DataType.Sqlite, ":memory:")
                .Build();
        });
        
        // 添加DynamicFreesql服务
        services.AddDynamicFreesql(options =>
        {
            options.EnableCache = true;
            options.MaxJoinDepth = 5;
            options.MaxPageSize = 1000;
        });
        
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证服务可以正确解析
        var modelMetadata = serviceProvider.GetRequiredService<IModelMetadata>();
        var pathParser = serviceProvider.GetRequiredService<IPathParser>();
        var expressionParser = serviceProvider.GetRequiredService<IExpressionParser>();
        var joinPlanner = serviceProvider.GetRequiredService<IJoinPlanner>();
        var filterBackend = serviceProvider.GetRequiredService<IFilterBackend>();
        var orderingBackend = serviceProvider.GetRequiredService<IOrderingBackend>();
        
        // 验证泛型服务
        var queryBuilder = serviceProvider.GetRequiredService<IQueryBuilder<TestEntity>>();
        var requestValidator = serviceProvider.GetRequiredService<RequestValidator<TestEntity>>();
        
        // 如果能执行到这里，说明所有类型都是正确的
        Console.WriteLine("所有类型验证通过！");
    }
}

/// <summary>
/// 测试实体
/// </summary>
public class TestEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 测试控制器
/// </summary>
public class TestController : ReadOnlyModelViewSet<TestEntity, int>
{
    public TestController(
        IFreeSql freeSql,
        IQueryBuilder<TestEntity> queryBuilder,
        IModelMetadata modelMetadata,
        PathValidator pathValidator,
        ILogger<ReadOnlyModelViewSet<TestEntity, int>> logger,
        DynamicFreesqlOptions options)
        : base(freeSql, queryBuilder, modelMetadata, pathValidator, logger, options)
    {
    }
}
