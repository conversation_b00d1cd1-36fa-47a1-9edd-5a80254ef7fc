<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>DynamicFreesql</PackageId>
    <Version>1.0.0</Version>
    <Authors>DynamicFreesql Team</Authors>
    <Description>FreeSql动态查询方案，支持动态Filter、Search、OrderBy，类似Django DRF的ReadOnlyModelViewSet</Description>
    <PackageTags>FreeSql;ORM;Dynamic;Query;Filter;Search;OrderBy</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FreeSql" Version="3.5.212" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.212" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
  </ItemGroup>

</Project>
