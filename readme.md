FreeSql 动态查询方案实现文档
一、目标
支持动态 Filter、Search、OrderBy，均支持跨实体导航（正向及反向）

配置项采用表达式 Expression<Func<TEntity, object>> + 枚举操作符，支持编译期检查

自动推导 Join 路径，自动生成多表 SQL（LeftJoin）

实现类似 DRF 的 ReadOnlyModelViewSet，只写配置即可完成复杂查询

防止SQL注入，限制过滤字段及操作符集合，配置白名单，限制 Join 深度

二、核心组件设计
组件	责任描述	核心类型或方法
ModelMetadata	扫描实体属性、外键、导航特性，收集元数据	EntityMeta, NavigationMeta
ExpressionParser	将 Expression<Func<TEntity, object>> 转换为字段路径字符串	GetPropertyPath<T>(Expression<Func<T, object>> expr)
PathParser	解析请求的过滤字段路径字符串（含操作符），校验字段及操作符合法性	ParseFilterPath(string path)
JoinPlanner	根据路径及元数据计算自动 Join 路径，防止重复 Join	PlanJoins(EntityMeta root, string[] pathSegments)
QueryBuilder	调用 FreeSql API 动态构造查询，应用 Join、Where、OrderBy	.Filter(), .OrderBy(), .Page(), .ToListAsync()
FilterOperator	枚举操作符定义，统一约束操作符类型	enum FilterOperator { Exact, IContains, Gte, Lte, ... }
FilterBackend	负责根据操作符生成具体 FreeSql Lambda / SQL 片段	ApplyFilters(ISelect<T>, FilterParams)
OrderingBackend	负责生成排序表达式及对应 Join	ApplyOrdering(ISelect<T>, OrderingParams)
ReadOnlyModelViewSet	Controller 基类，接收请求参数，解析过滤、排序条件，构建并执行查询	List(), Get(), 配置属性：FilterSetFields等

三、详细实现步骤
1. ModelMetadata 扫描与缓存
   通过反射扫描实体类所有属性

识别 [Navigate] 特性，建立正向关联

识别实体中带有外键的属性，建立关系映射

自动推断反向关联（即集合导航）

缓存到 EntityMeta 结构中，包含：

实体类型

表名（FreeSql 中可从 IFreeSql.CodeFirst.GetTableByEntity 获取）

属性映射（属性名→列名）

导航关系（导航名→关联实体、外键字段、Join类型）

2. ExpressionParser — 表达式转路径字符串
   示例代码：

csharp
复制
编辑
public static string GetPropertyPath<T>(Expression<Func<T, object>> expr)
{
var parts = new List<string>();
Expression body = expr.Body;
if (body is UnaryExpression unary && unary.NodeType == ExpressionType.Convert)
body = unary.Operand;

    while (body is MemberExpression member)
    {
        parts.Insert(0, member.Member.Name);
        body = member.Expression;
    }

    return string.Join("__", parts); // 驼峰转下划线可留给后续翻译
}
3. PathParser — 解析请求的字段路径
   接收请求参数中的字段名，如 author__name__icontains

拆分为字段路径 ["author", "name"] 和操作符 icontains

校验字段存在于 EntityMeta，操作符在允许列表内

返回结构：

csharp
复制
编辑
class FilterPath
{
public string[] Segments;       // 如 ["author", "name"]
public FilterOperator Operator; // 枚举，如 IContains
}
4. JoinPlanner — 自动 Join 规划
   根据字段路径，结合元数据确定需要多少个 join

维护 join 别名池，避免重复 join

生成 JoinPlan，包含：

Join 实体类型

Join 条件表达式（使用 Source Generator 预生成 lambda）

Join 别名

支持多级 join 和反向 join（如 author__books）

5. QueryBuilder — 构建完整查询
   维护内部 _ISelect<TEntity> 对象

在 Filter()、OrderBy() 时调用 JoinPlanner 规划 Join

应用 FilterBackend 和 OrderingBackend 生成 Lambda 表达式或参数化 SQL

支持分页

支持 .ToListAsync(), .CountAsync() 等方法

6. FilterOperator 枚举与 FilterBackend
   csharp
   复制
   编辑
   public enum FilterOperator
   {
   Exact,
   IContains,
   Contains,
   StartsWith,
   EndsWith,
   Gte,
   Lte,
   In,
   IsNull
   }
   根据不同操作符，FilterBackend 生成对应的表达式

例如，IContains 生成类似 (x => x.Field.ToLower().Contains(value.ToLower())) 的表达式

支持对多种数据类型（字符串、数字、日期）的智能处理

7. ReadOnlyModelViewSet 示例设计
   csharp
   复制
   编辑
   public abstract class ReadOnlyModelViewSet<TEntity, TKey> : ControllerBase
   {
   protected abstract ISelect<TEntity> Query { get; }

   protected virtual Dictionary<Expression<Func<TEntity, object>>, FilterOperator[]> FilterSetFields => new();

   protected virtual Expression<Func<TEntity, object>>[] SearchFields => Array.Empty<Expression<Func<TEntity, object>>>();

   protected virtual Expression<Func<TEntity, object>>[] OrderingFields => Array.Empty<Expression<Func<TEntity, object>>>();

   [HttpGet]
   public async Task<IActionResult> List([FromQuery] QueryParams queryParams)
   {
   var builder = new QueryBuilder<TEntity>(Query);
   // 应用过滤
   builder.ApplyFilters(queryParams.Filters, FilterSetFields);
   // 应用搜索
   builder.ApplySearch(queryParams.Search, SearchFields);
   // 应用排序
   builder.ApplyOrdering(queryParams.Ordering, OrderingFields);
   // 应用分页
   builder.ApplyPaging(queryParams.Page, queryParams.PageSize);

        var total = await builder.CountAsync();
        var list = await builder.ToListAsync();

        return Ok(new { total, list });
   }
   }
   四、安全与扩展
   字段与操作符白名单避免用户传入非法参数

最大Join深度防止恶意多级关联导致性能崩溃

参数化SQL避免SQL注入

日志审计监控动态生成SQL，避免异常情况

插件扩展支持用户扩展自定义操作符或过滤逻辑

五、性能优化建议
利用 Source Generator 预生成实体间 Join 关联表达式，避免运行时构造复杂表达式树

缓存 JoinPlan 和解析结果，减少重复计算

分页查询分离 Count 与 Data，防止 Count 查询性能瓶颈

对常用查询可考虑二级缓存（Redis等）

六、示例请求格式
请求参数	说明	解析示例
title__icontains=python	title 字段模糊匹配	FilterPath: ["title"], IContains
author__name=张三	author 表的 name 字段精确匹配	FilterPath: ["author", "name"], Exact
ordering=-published_date	按日期倒序排序	OrderingFields: ["published_date"], Desc
search=python	全文搜索（在 SearchFields 中字段）	模糊查询所有 SearchFields 字段

七、后续演进
支持复杂聚合、GroupBy、Having

支持动态 Include/预加载（类似 select_related/prefetch_related）

支持更复杂的反向导航懒加载集合

支持多数据库、多租户上下文动态切换


