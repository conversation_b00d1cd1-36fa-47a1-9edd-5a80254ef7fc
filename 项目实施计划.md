# FreeSql 动态查询方案 - 项目实施计划

## 📋 项目概述

### 项目目标
实现一个基于 FreeSql 的动态查询方案，支持：
- 动态 Filter、Search、OrderBy，均支持跨实体导航（正向及反向）
- 配置项采用表达式 Expression<Func<TEntity, object>> + 枚举操作符，支持编译期检查
- 自动推导 Join 路径，自动生成多表 SQL（LeftJoin）
- 实现类似 DRF 的 ReadOnlyModelViewSet，只写配置即可完成复杂查询
- 防止SQL注入，限制过滤字段及操作符集合，配置白名单，限制 Join 深度

### 技术栈
- **.NET 8.0** - 现代化的 .NET 平台
- **FreeSql 3.5.212** - 最新版本的 FreeSql ORM
- **ASP.NET Core** - Web API 框架
- **xUnit** - 单元测试框架

### 项目可行性评估
✅ **高度可行** - 技术栈成熟，架构设计合理，安全考虑周全

## 🏗️ 项目架构

### 核心组件
| 组件 | 职责描述 | 核心类型或方法 |
|------|----------|----------------|
| ModelMetadata | 扫描实体属性、外键、导航特性，收集元数据 | EntityMeta, NavigationMeta |
| ExpressionParser | 将 Expression<Func<TEntity, object>> 转换为字段路径字符串 | GetPropertyPath<T>(Expression<Func<T, object>> expr) |
| PathParser | 解析请求的过滤字段路径字符串（含操作符），校验字段及操作符合法性 | ParseFilterPath(string path) |
| JoinPlanner | 根据路径及元数据计算自动 Join 路径，防止重复 Join | PlanJoins(EntityMeta root, string[] pathSegments) |
| QueryBuilder | 调用 FreeSql API 动态构造查询，应用 Join、Where、OrderBy | .Filter(), .OrderBy(), .Page(), .ToListAsync() |
| FilterOperator | 枚举操作符定义，统一约束操作符类型 | enum FilterOperator { Exact, IContains, Gte, Lte, ... } |
| FilterBackend | 负责根据操作符生成具体 FreeSql Lambda / SQL 片段 | ApplyFilters(ISelect<T>, FilterParams) |
| OrderingBackend | 负责生成排序表达式及对应 Join | ApplyOrdering(ISelect<T>, OrderingParams) |
| ReadOnlyModelViewSet | Controller 基类，接收请求参数，解析过滤、排序条件，构建并执行查询 | List(), Get(), 配置属性：FilterSetFields等 |

## 📅 实施阶段

### 阶段 1：项目基础设施搭建 (预计 1 周)
**目标**：建立项目基础架构和开发环境

**主要任务**：
- 升级项目到最新 FreeSql 版本 (3.5.212)
- 创建项目结构（核心库、测试项目、示例项目）
- 定义核心接口和基础类
- 配置依赖注入容器

**交付物**：
- 完整的项目结构
- 基础接口定义
- 开发环境配置

### 阶段 2：元数据扫描系统 (预计 1 周)
**目标**：实现实体元数据扫描和缓存机制

**主要任务**：
- 实现 EntityMeta 和 NavigationMeta 数据结构
- 实现实体属性扫描器（通过反射）
- 实现外键关系识别和反向关联推断
- 实现元数据缓存机制

**交付物**：
- 完整的元数据扫描系统
- 缓存机制
- 单元测试

### 阶段 3：表达式解析引擎 (预计 1 周)
**目标**：实现 Expression 转路径字符串和请求参数解析

**主要任务**：
- 实现 ExpressionParser 核心类
- 实现 FilterPath 数据结构
- 实现 PathParser 核心类
- 实现字段路径校验逻辑
- 实现复杂表达式处理

**交付物**：
- 表达式解析引擎
- 请求参数解析器
- 单元测试

### 阶段 4：查询构建核心 (预计 2 周)
**目标**：实现动态查询构建和操作符处理

**主要任务**：
- 实现 FilterOperator 枚举和相关数据结构
- 实现 JoinPlanner 核心类和 Join 别名池管理
- 实现 FilterBackend 和 OrderingBackend 核心类
- 实现 QueryBuilder 核心类
- 实现多级 Join 支持和分页功能
- 实现查询执行方法

**交付物**：
- 完整的查询构建引擎
- 支持复杂 Join 的查询系统
- 单元测试

### 阶段 5：控制器集成 (预计 1 周)
**目标**：实现 ReadOnlyModelViewSet 基类和请求处理

**主要任务**：
- 实现 QueryParams 数据结构
- 实现 ReadOnlyModelViewSet 基类
- 实现过滤器、搜索、排序字段配置
- 实现 List 和 Get 接口方法
- 实现响应格式化和请求参数验证

**交付物**：
- 完整的控制器基类
- 统一的 API 接口
- 单元测试

### 阶段 6：安全性和验证 (预计 1 周)
**目标**：实现安全防护和验证机制

**主要任务**：
- 实现字段和操作符白名单机制
- 实现 Join 深度限制
- 实现 SQL 注入防护
- 实现审计日志系统
- 实现输入参数清理

**交付物**：
- 完整的安全防护系统
- 审计日志功能
- 安全性测试

### 阶段 7：测试和质量保证 (预计 2 周)
**目标**：实现全面的测试覆盖和质量保证

**主要任务**：
- 创建测试项目结构
- 编写所有核心组件的单元测试
- 编写安全性功能测试
- 编写集成测试和性能测试
- 编写边界条件测试

**交付物**：
- 完整的测试套件
- 测试覆盖率报告
- 性能测试报告

### 阶段 8：示例和文档 (预计 1 周)
**目标**：创建示例项目和完整文档

**主要任务**：
- 创建示例项目和实体模型
- 实现示例控制器
- 创建数据库初始化脚本
- 编写 API 使用文档和快速入门指南
- 编写最佳实践文档和故障排查指南

**交付物**：
- 完整的示例项目
- 详细的使用文档
- 最佳实践指南

## 🎯 关键里程碑

- **里程碑 1** (第 2 周末)：基础架构完成，可以扫描实体元数据
- **里程碑 2** (第 4 周末)：表达式解析完成，可以处理简单查询
- **里程碑 3** (第 6 周末)：Join 规划完成，支持复杂关联查询
- **里程碑 4** (第 7 周末)：控制器集成完成，可以处理 HTTP 请求
- **里程碑 5** (第 8 周末)：安全性完善，生产就绪

## ⚠️ 技术风险点

1. **Expression 表达式树的复杂性**
   - 风险：复杂表达式解析可能出现边界情况
   - 缓解：充分的单元测试，逐步增加复杂度

2. **多级 Join 的性能影响**
   - 风险：深层次 Join 可能导致性能问题
   - 缓解：实现 Join 深度限制，性能测试验证

3. **动态 SQL 的安全性**
   - 风险：可能存在 SQL 注入风险
   - 缓解：严格的参数化查询，白名单机制

4. **缓存策略的正确性**
   - 风险：元数据缓存可能导致数据不一致
   - 缓解：合理的缓存失效策略，充分测试

## 📊 预期成果

### 功能特性
- ✅ 支持动态过滤、搜索、排序
- ✅ 支持跨实体导航查询
- ✅ 编译期类型检查
- ✅ 自动 Join 路径推导
- ✅ SQL 注入防护
- ✅ 性能优化机制

### 性能指标
- 单次查询响应时间 < 100ms（简单查询）
- 单次查询响应时间 < 500ms（复杂 Join 查询）
- 支持并发请求数 > 1000/秒
- 内存使用稳定，无内存泄漏

### 代码质量
- 单元测试覆盖率 > 90%
- 代码复杂度控制在合理范围
- 遵循 SOLID 原则
- 完整的文档和示例

## 🚀 后续演进计划

1. **支持复杂聚合、GroupBy、Having**
2. **支持动态 Include/预加载**
3. **支持更复杂的反向导航懒加载集合**
4. **支持多数据库、多租户上下文动态切换**
5. **性能监控和优化工具**
6. **可视化查询构建器**

---

**项目预计总工期：8 周**  
**团队规模建议：2-3 名开发人员**  
**技术难度：中高级**
